#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - مشغل التطبيق
Ma'aris Al Jahra - Application Launcher

مشغل بسيط لتطبيق سطح المكتب مع فحص المتطلبات
Simple launcher for desktop application with requirements check
"""

import sys
import subprocess
import os

def install_requirements():
    """Install required packages."""
    print("📦 تثبيت المتطلبات...")
    print("📦 Installing requirements...")
    
    try:
        # Try to install PySide6
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PySide6", "--quiet"])
        print("✅ تم تثبيت PySide6 بنجاح")
        print("✅ PySide6 installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت PySide6")
        print("❌ Failed to install PySide6")
        return False

def check_pyside6():
    """Check if PySide6 is available."""
    try:
        import PySide6
        return True
    except ImportError:
        return False

def main():
    """Main launcher function."""
    print("========================================")
    print("   معاريس الجهراء - تطبيق سطح المكتب")
    print("   Ma'aris Al Jahra - Desktop App")
    print("========================================")
    print()
    
    print("🔍 فحص متطلبات النظام...")
    print("🔍 Checking system requirements...")
    
    # Check if PySide6 is installed
    if not check_pyside6():
        print("⚠️  PySide6 غير مثبت")
        print("⚠️  PySide6 is not installed")
        
        response = input("هل تريد تثبيته الآن؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم', 'ن']:
            if not install_requirements():
                print("❌ فشل في تثبيت المتطلبات")
                print("❌ Failed to install requirements")
                input("اضغط Enter للخروج...")
                return
        else:
            print("❌ لا يمكن تشغيل التطبيق بدون PySide6")
            print("❌ Cannot run application without PySide6")
            input("اضغط Enter للخروج...")
            return
    
    print("✅ جميع المتطلبات متوفرة")
    print("✅ All requirements available")
    print()
    
    print("🚀 تشغيل التطبيق...")
    print("🚀 Starting application...")
    print()
    
    try:
        # Import and run the main application
        from main_desktop import main
        main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print(f"❌ Error running application: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
