using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;
using MaarisAlJahra.Helpers;
using MaarisAlJahra.Models;
using Bunifu.UI.WinForms;

namespace MaarisAlJahra.Forms
{
    public partial class DebtsForm : Form
    {
        private DataGridView debtsGrid;
        private BunifuTextBox customerNameTextBox;
        private BunifuTextBox customerPhoneTextBox;
        private BunifuTextBox amountTextBox;
        private BunifuTextBox descriptionTextBox;
        private Button addButton;
        private Button markPaidButton;
        private Button deleteButton;
        private Label titleLabel;
        private Label totalDebtsLabel;
        private int selectedDebtId = -1;

        public DebtsForm()
        {
            InitializeComponent();
            SetupForm();
            SetupControls();
            LoadDebts();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1400, 800);
            this.FormBorderStyle = FormBorderStyle.None;
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Name = "DebtsForm";
            this.Text = "إدارة الديون";

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer, true);

            StyleHelper.ApplyGradientBackground(this);
        }

        private void SetupControls()
        {
            // Title
            titleLabel = new Label
            {
                Text = "إدارة الديون",
                Font = new Font("Segoe UI", 28, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                AutoSize = true,
                Location = new Point(50, 30)
            };

            // Total debts label
            totalDebtsLabel = new Label
            {
                Text = "إجمالي الديون: 0",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = StyleHelper.WarningColor,
                BackColor = Color.Transparent,
                AutoSize = true,
                Location = new Point(50, 80)
            };

            // Input Controls
            CreateInputControls();

            // Action Buttons
            CreateActionButtons();

            // Data Grid
            CreateDataGrid();

            this.Controls.Add(titleLabel);
            this.Controls.Add(totalDebtsLabel);
            this.Controls.Add(customerNameTextBox);
            this.Controls.Add(customerPhoneTextBox);
            this.Controls.Add(amountTextBox);
            this.Controls.Add(descriptionTextBox);
            this.Controls.Add(addButton);
            this.Controls.Add(markPaidButton);
            this.Controls.Add(deleteButton);
            this.Controls.Add(debtsGrid);
        }

        private void CreateInputControls()
        {
            var startY = 140;
            var spacing = 70;

            customerNameTextBox = CreateStyledTextBox("اسم العميل", new Point(50, startY));
            customerPhoneTextBox = CreateStyledTextBox("رقم الهاتف", new Point(50, startY + spacing));
            amountTextBox = CreateStyledTextBox("مبلغ الدين", new Point(50, startY + spacing * 2));
            descriptionTextBox = CreateStyledTextBox("الوصف", new Point(50, startY + spacing * 3));
        }

        private BunifuTextBox CreateStyledTextBox(string placeholder, Point location)
        {
            var textBox = new BunifuTextBox
            {
                Size = new Size(300, 50),
                Location = location,
                PlaceholderText = placeholder,
                Font = new Font("Segoe UI", 12),
                BorderColorActive = StyleHelper.AccentColor,
                BorderColorDisabled = StyleHelper.LightColor,
                BorderColorHover = StyleHelper.AccentColor,
                BorderColorIdle = StyleHelper.LightColor,
                FillColor = Color.White
            };

            return textBox;
        }

        private void CreateActionButtons()
        {
            var buttonY = 450;
            var buttonSpacing = 120;

            addButton = StyleHelper.CreateStyledButton("إضافة", StyleHelper.SuccessColor,
                new Size(100, 40), AddDebt_Click);
            addButton.Location = new Point(50, buttonY);

            markPaidButton = StyleHelper.CreateStyledButton("تسديد", StyleHelper.AccentColor,
                new Size(100, 40), MarkPaid_Click);
            markPaidButton.Location = new Point(50 + buttonSpacing, buttonY);

            deleteButton = StyleHelper.CreateStyledButton("حذف", StyleHelper.DangerColor,
                new Size(100, 40), DeleteDebt_Click);
            deleteButton.Location = new Point(50 + buttonSpacing * 2, buttonY);
        }

        private void CreateDataGrid()
        {
            debtsGrid = new DataGridView
            {
                Location = new Point(400, 140),
                Size = new Size(950, 600),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            StyleHelper.StyleDataGridView(debtsGrid);

            debtsGrid.SelectionChanged += (s, e) =>
            {
                if (debtsGrid.SelectedRows.Count > 0)
                {
                    var row = debtsGrid.SelectedRows[0];
                    selectedDebtId = (int)row.Cells["Id"].Value;
                    customerNameTextBox.Text = row.Cells["CustomerName"].Value?.ToString() ?? "";
                    customerPhoneTextBox.Text = row.Cells["CustomerPhone"].Value?.ToString() ?? "";
                    amountTextBox.Text = row.Cells["Amount"].Value?.ToString() ?? "";
                    descriptionTextBox.Text = row.Cells["Description"].Value?.ToString() ?? "";
                }
            };
        }

        private async void LoadDebts()
        {
            try
            {
                using var context = new DatabaseContext();
                var debts = await context.Debts.Where(d => !d.IsPaid)
                    .OrderByDescending(d => d.CreatedAt).ToListAsync();

                debtsGrid.DataSource = debts.Select(d => new
                {
                    d.Id,
                    d.CustomerName,
                    d.CustomerPhone,
                    d.Amount,
                    d.Description,
                    CreatedAt = d.CreatedAt.ToString("yyyy-MM-dd"),
                    Status = d.IsPaid ? "مسدد" : "غير مسدد"
                }).ToList();

                debtsGrid.Columns["Id"].Visible = false;
                debtsGrid.Columns["CustomerName"].HeaderText = "اسم العميل";
                debtsGrid.Columns["CustomerPhone"].HeaderText = "الهاتف";
                debtsGrid.Columns["Amount"].HeaderText = "المبلغ";
                debtsGrid.Columns["Description"].HeaderText = "الوصف";
                debtsGrid.Columns["CreatedAt"].HeaderText = "تاريخ الإنشاء";
                debtsGrid.Columns["Status"].HeaderText = "الحالة";

                // Calculate total debts
                var totalAmount = debts.Sum(d => d.Amount);
                totalDebtsLabel.Text = $"إجمالي الديون: {totalAmount:F2}";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void AddDebt_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                await AnimationHelper.ScaleButton(addButton);

                using var context = new DatabaseContext();
                var debt = new Debt
                {
                    CustomerName = customerNameTextBox.Text.Trim(),
                    CustomerPhone = customerPhoneTextBox.Text.Trim(),
                    Amount = decimal.Parse(amountTextBox.Text.Trim()),
                    Description = descriptionTextBox.Text.Trim(),
                    CreatedAt = DateTime.Now,
                    IsPaid = false
                };

                context.Debts.Add(debt);
                await context.SaveChangesAsync();

                ClearInputs();
                await LoadDebts();

                MessageBox.Show("تم إضافة الدين بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                await AnimationHelper.ShakeControl(addButton);
                MessageBox.Show($"خطأ في إضافة الدين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void MarkPaid_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedDebtId == -1)
                {
                    MessageBox.Show("يرجى اختيار دين لتسديده", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من تسديد هذا الدين؟", "تأكيد التسديد",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await AnimationHelper.ScaleButton(markPaidButton);

                    using var context = new DatabaseContext();
                    var debt = await context.Debts.FindAsync(selectedDebtId);

                    if (debt != null)
                    {
                        debt.IsPaid = true;
                        debt.PaidAt = DateTime.Now;
                        await context.SaveChangesAsync();

                        ClearInputs();
                        await LoadDebts();

                        MessageBox.Show("تم تسديد الدين بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                await AnimationHelper.ShakeControl(markPaidButton);
                MessageBox.Show($"خطأ في تسديد الدين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteDebt_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedDebtId == -1)
                {
                    MessageBox.Show("يرجى اختيار دين للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف هذا الدين؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await AnimationHelper.ScaleButton(deleteButton);

                    using var context = new DatabaseContext();
                    var debt = await context.Debts.FindAsync(selectedDebtId);

                    if (debt != null)
                    {
                        context.Debts.Remove(debt);
                        await context.SaveChangesAsync();

                        ClearInputs();
                        await LoadDebts();

                        MessageBox.Show("تم حذف الدين بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                await AnimationHelper.ShakeControl(deleteButton);
                MessageBox.Show($"خطأ في حذف الدين: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(customerNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                customerNameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(amountTextBox.Text) ||
                !decimal.TryParse(amountTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                amountTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearInputs()
        {
            customerNameTextBox.Text = "";
            customerPhoneTextBox.Text = "";
            amountTextBox.Text = "";
            descriptionTextBox.Text = "";
            selectedDebtId = -1;
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                this.Close();
            }
            base.OnKeyDown(e);
        }
    }
}