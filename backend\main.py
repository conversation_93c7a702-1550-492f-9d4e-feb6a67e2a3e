#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - Simple Backend Server
Simple HTTP server for the Ma'aris Al Jahra application
"""

import http.server
import socketserver
import json
import sqlite3
from datetime import datetime
from urllib.parse import urlparse, parse_qs

# Database path
DB_PATH = "../maaris_aljahra.db"

class APIHandler(http.server.BaseHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests."""
        parsed_path = urlparse(self.path)
        path = parsed_path.path

        # Add CORS headers
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        try:
            if path == '/':
                response = {"message": "Ma'aris Al Jahra API is running!", "version": "2.0.0"}
            elif path == '/api/customers':
                response = self.get_customers()
            elif path == '/api/orders':
                response = self.get_orders()
            elif path == '/api/debts':
                response = self.get_debts()
            elif path == '/api/statistics/daily':
                response = self.get_daily_statistics()
            elif path == '/api/statistics/monthly':
                response = self.get_monthly_statistics()
            elif path.startswith('/api/customers/search/'):
                query = path.split('/')[-1]
                response = self.search_customers(query)
            else:
                response = {"error": "Not found"}

            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        except Exception as e:
            error_response = {"error": str(e)}
            self.wfile.write(json.dumps(error_response).encode('utf-8'))

    def do_POST(self):
        """Handle POST requests."""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)

        # Add CORS headers
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        try:
            data = json.loads(post_data.decode('utf-8'))
            parsed_path = urlparse(self.path)
            path = parsed_path.path

            if path == '/api/customers':
                response = self.create_customer(data)
            elif path == '/api/orders':
                response = self.create_order(data)
            else:
                response = {"error": "Not found"}

            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        except Exception as e:
            error_response = {"error": str(e)}
            self.wfile.write(json.dumps(error_response).encode('utf-8'))

    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS."""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def get_db_connection(self):
        """Get database connection."""
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row
        return conn

    def get_customers(self):
        """Get all customers."""
        conn = self.get_db_connection()
        try:
            cursor = conn.execute("""
                SELECT id, name, phone, chest, shoulder, hand, waist, hip,
                       inseam, neck, height, created_at
                FROM customers
                ORDER BY name
            """)
            customers = []
            for row in cursor.fetchall():
                customers.append({
                    "id": row["id"],
                    "name": row["name"],
                    "phone": row["phone"],
                    "chest": row["chest"],
                    "shoulder": row["shoulder"],
                    "hand": row["hand"],
                    "waist": row["waist"],
                    "hip": row["hip"],
                    "inseam": row["inseam"],
                    "neck": row["neck"],
                    "height": row["height"],
                    "created_at": row["created_at"]
                })
            return customers
        finally:
            conn.close()

@app.post("/api/customers", response_model=Customer)
async def create_customer(customer: CustomerCreate):
    """Create a new customer."""
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            INSERT INTO customers (name, phone, chest, shoulder, hand, waist, hip, inseam, neck, height)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            customer.name, customer.phone, customer.chest, customer.shoulder,
            customer.hand, customer.waist, customer.hip, customer.inseam,
            customer.neck, customer.height
        ))
        conn.commit()

        # Get the created customer
        customer_id = cursor.lastrowid
        cursor = conn.execute("""
            SELECT id, name, phone, chest, shoulder, hand, waist, hip,
                   inseam, neck, height, created_at
            FROM customers WHERE id = ?
        """, (customer_id,))
        row = cursor.fetchone()

        return {
            "id": row["id"],
            "name": row["name"],
            "phone": row["phone"],
            "chest": row["chest"],
            "shoulder": row["shoulder"],
            "hand": row["hand"],
            "waist": row["waist"],
            "hip": row["hip"],
            "inseam": row["inseam"],
            "neck": row["neck"],
            "height": row["height"],
            "created_at": row["created_at"]
        }
    finally:
        conn.close()

@app.get("/api/customers/{customer_id}", response_model=Customer)
async def get_customer(customer_id: int):
    """Get a specific customer."""
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            SELECT id, name, phone, chest, shoulder, hand, waist, hip,
                   inseam, neck, height, created_at
            FROM customers WHERE id = ?
        """, (customer_id,))
        row = cursor.fetchone()

        if not row:
            raise HTTPException(status_code=404, detail="Customer not found")

        return {
            "id": row["id"],
            "name": row["name"],
            "phone": row["phone"],
            "chest": row["chest"],
            "shoulder": row["shoulder"],
            "hand": row["hand"],
            "waist": row["waist"],
            "hip": row["hip"],
            "inseam": row["inseam"],
            "neck": row["neck"],
            "height": row["height"],
            "created_at": row["created_at"]
        }
    finally:
        conn.close()

@app.put("/api/customers/{customer_id}", response_model=Customer)
async def update_customer(customer_id: int, customer: CustomerCreate):
    """Update a customer."""
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            UPDATE customers
            SET name=?, phone=?, chest=?, shoulder=?, hand=?, waist=?, hip=?, inseam=?, neck=?, height=?
            WHERE id=?
        """, (
            customer.name, customer.phone, customer.chest, customer.shoulder,
            customer.hand, customer.waist, customer.hip, customer.inseam,
            customer.neck, customer.height, customer_id
        ))

        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail="Customer not found")

        conn.commit()

        # Return updated customer
        return await get_customer(customer_id)
    finally:
        conn.close()

@app.delete("/api/customers/{customer_id}")
async def delete_customer(customer_id: int):
    """Delete a customer."""
    conn = get_db_connection()
    try:
        cursor = conn.execute("DELETE FROM customers WHERE id = ?", (customer_id,))

        if cursor.rowcount == 0:
            raise HTTPException(status_code=404, detail="Customer not found")

        conn.commit()
        return {"message": "Customer deleted successfully"}
    finally:
        conn.close()

# Search customers
@app.get("/api/customers/search/{query}", response_model=List[Customer])
async def search_customers(query: str):
    """Search customers by name or phone."""
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            SELECT id, name, phone, chest, shoulder, hand, waist, hip,
                   inseam, neck, height, created_at
            FROM customers
            WHERE name LIKE ? OR phone LIKE ?
            ORDER BY name
        """, (f"%{query}%", f"%{query}%"))

        customers = []
        for row in cursor.fetchall():
            customers.append({
                "id": row["id"],
                "name": row["name"],
                "phone": row["phone"],
                "chest": row["chest"],
                "shoulder": row["shoulder"],
                "hand": row["hand"],
                "waist": row["waist"],
                "hip": row["hip"],
                "inseam": row["inseam"],
                "neck": row["neck"],
                "height": row["height"],
                "created_at": row["created_at"]
            })
        return customers
    finally:
        conn.close()

# Order endpoints
@app.get("/api/orders", response_model=List[Order])
async def get_orders():
    """Get all orders."""
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            SELECT o.id, o.customer_id, o.order_type, o.total_price, o.payment,
                   (o.total_price - o.payment) as remaining, o.created_at,
                   c.name as customer_name
            FROM orders o
            JOIN customers c ON o.customer_id = c.id
            ORDER BY o.created_at DESC
        """)
        orders = []
        for row in cursor.fetchall():
            orders.append({
                "id": row["id"],
                "customer_id": row["customer_id"],
                "order_type": row["order_type"],
                "total_price": row["total_price"],
                "payment": row["payment"],
                "remaining": row["remaining"],
                "created_at": row["created_at"]
            })
        return orders
    finally:
        conn.close()

@app.post("/api/orders", response_model=Order)
async def create_order(order: OrderCreate):
    """Create a new order."""
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            INSERT INTO orders (customer_id, order_type, total_price, payment)
            VALUES (?, ?, ?, ?)
        """, (order.customer_id, order.order_type, order.total_price, order.payment))
        conn.commit()

        order_id = cursor.lastrowid
        cursor = conn.execute("""
            SELECT id, customer_id, order_type, total_price, payment,
                   (total_price - payment) as remaining, created_at
            FROM orders WHERE id = ?
        """, (order_id,))
        row = cursor.fetchone()

        # If there's remaining balance, create debt record
        remaining = order.total_price - order.payment
        if remaining > 0:
            conn.execute("""
                INSERT INTO debts (customer_id, amount, payment)
                VALUES (?, ?, ?)
            """, (order.customer_id, remaining, 0))
            conn.commit()

        return {
            "id": row["id"],
            "customer_id": row["customer_id"],
            "order_type": row["order_type"],
            "total_price": row["total_price"],
            "payment": row["payment"],
            "remaining": row["remaining"],
            "created_at": row["created_at"]
        }
    finally:
        conn.close()

# Debt endpoints
@app.get("/api/debts", response_model=List[Debt])
async def get_debts():
    """Get all debts."""
    conn = get_db_connection()
    try:
        cursor = conn.execute("""
            SELECT d.id, d.customer_id, d.amount, d.payment,
                   (d.amount - d.payment) as remaining, d.created_at,
                   c.name as customer_name
            FROM debts d
            JOIN customers c ON d.customer_id = c.id
            WHERE (d.amount - d.payment) > 0
            ORDER BY d.created_at DESC
        """)
        debts = []
        for row in cursor.fetchall():
            debts.append({
                "id": row["id"],
                "customer_id": row["customer_id"],
                "amount": row["amount"],
                "payment": row["payment"],
                "remaining": row["remaining"],
                "created_at": row["created_at"]
            })
        return debts
    finally:
        conn.close()

# Statistics endpoints
@app.get("/api/statistics/daily")
async def get_daily_statistics():
    """Get daily statistics."""
    conn = get_db_connection()
    try:
        today = datetime.now().strftime('%Y-%m-%d')
        cursor = conn.execute("""
            SELECT
                COUNT(*) as total_orders,
                COALESCE(SUM(total_price), 0) as total_amount,
                COALESCE(SUM(payment), 0) as paid_amount,
                COALESCE(SUM(total_price - payment), 0) as remaining_amount
            FROM orders
            WHERE DATE(created_at) = ?
        """, (today,))
        row = cursor.fetchone()

        return {
            "total_orders": row["total_orders"],
            "total_amount": row["total_amount"],
            "paid_amount": row["paid_amount"],
            "remaining_amount": row["remaining_amount"]
        }
    finally:
        conn.close()

@app.get("/api/statistics/monthly")
async def get_monthly_statistics():
    """Get monthly statistics."""
    conn = get_db_connection()
    try:
        current_month = datetime.now().strftime('%Y-%m')
        cursor = conn.execute("""
            SELECT
                COUNT(*) as total_orders,
                COALESCE(SUM(total_price), 0) as total_amount,
                COALESCE(SUM(payment), 0) as paid_amount,
                COALESCE(SUM(total_price - payment), 0) as remaining_amount
            FROM orders
            WHERE strftime('%Y-%m', created_at) = ?
        """, (current_month,))
        row = cursor.fetchone()

        return {
            "total_orders": row["total_orders"],
            "total_amount": row["total_amount"],
            "paid_amount": row["paid_amount"],
            "remaining_amount": row["remaining_amount"]
        }
    finally:
        conn.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)

