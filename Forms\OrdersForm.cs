using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;
using MaarisAlJahra.Helpers;
using MaarisAlJahra.Models;

namespace MaarisAlJahra.Forms
{
    public partial class OrdersForm : Form
    {
        private DataGridView ordersGrid;
        private TextBox customerNameTextBox;
        private TextBox customerPhoneTextBox;
        private TextBox orderTypeTextBox;
        private TextBox totalPriceTextBox;
        private TextBox paymentTextBox;
        private Button addButton;
        private Button updateButton;
        private Button deleteButton;
        private Label titleLabel;
        private Label remainingLabel;
        private int selectedOrderId = -1;

        public OrdersForm()
        {
            InitializeComponent();
            SetupForm();
            SetupControls();
            LoadOrders();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1400, 800);
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.WindowState = FormWindowState.Normal;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.Name = "OrdersForm";
            this.Text = "إدارة الطلبات";

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer, true);

            StyleHelper.ApplyGradientBackground(this);
        }

        private void SetupControls()
        {
            // Title
            titleLabel = new Label
            {
                Text = "إدارة الطلبات",
                Font = new Font("Segoe UI", 28, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                AutoSize = true,
                Location = new Point(50, 30)
            };

            // Input Controls
            CreateInputControls();

            // Action Buttons
            CreateActionButtons();

            // Data Grid
            CreateDataGrid();

            this.Controls.Add(titleLabel);
            this.Controls.Add(customerNameTextBox);
            this.Controls.Add(customerPhoneTextBox);
            this.Controls.Add(orderTypeTextBox);
            this.Controls.Add(totalPriceTextBox);
            this.Controls.Add(paymentTextBox);
            this.Controls.Add(remainingLabel);
            this.Controls.Add(addButton);
            this.Controls.Add(updateButton);
            this.Controls.Add(deleteButton);
            this.Controls.Add(ordersGrid);
        }

        private void CreateInputControls()
        {
            var startY = 120;
            var spacing = 70;

            customerNameTextBox = CreateStyledTextBox("اسم العميل", new Point(50, startY));
            customerPhoneTextBox = CreateStyledTextBox("رقم الهاتف", new Point(50, startY + spacing));
            orderTypeTextBox = CreateStyledTextBox("نوع الطلب", new Point(50, startY + spacing * 2));
            totalPriceTextBox = CreateStyledTextBox("السعر الإجمالي", new Point(50, startY + spacing * 3));
            paymentTextBox = CreateStyledTextBox("المبلغ المدفوع", new Point(50, startY + spacing * 4));

            // Remaining amount label
            remainingLabel = new Label
            {
                Text = "المبلغ المتبقي: 0",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = StyleHelper.WarningColor,
                BackColor = Color.Transparent,
                AutoSize = true,
                Location = new Point(50, startY + spacing * 5)
            };

            // Calculate remaining when payment changes
            totalPriceTextBox.TextChanged += CalculateRemaining;
            paymentTextBox.TextChanged += CalculateRemaining;
        }

        private TextBox CreateStyledTextBox(string placeholder, Point location)
        {
            var textBox = new TextBox
            {
                Size = new Size(300, 30),
                Location = location,
                Font = new Font("Segoe UI", 12),
                BackColor = Color.White,
                ForeColor = Color.Black,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Add placeholder functionality
            textBox.Text = placeholder;
            textBox.ForeColor = Color.Gray;

            textBox.Enter += (s, e) =>
            {
                if (textBox.Text == placeholder)
                {
                    textBox.Text = "";
                    textBox.ForeColor = Color.Black;
                }
            };

            textBox.Leave += (s, e) =>
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    textBox.Text = placeholder;
                    textBox.ForeColor = Color.Gray;
                }
            };

            return textBox;
        }

        private void CreateActionButtons()
        {
            var buttonY = 520;
            var buttonSpacing = 120;

            addButton = StyleHelper.CreateStyledButton("إضافة", StyleHelper.SuccessColor,
                new Size(100, 40), AddOrder_Click);
            addButton.Location = new Point(50, buttonY);

            updateButton = StyleHelper.CreateStyledButton("تحديث", StyleHelper.AccentColor,
                new Size(100, 40), UpdateOrder_Click);
            updateButton.Location = new Point(50 + buttonSpacing, buttonY);

            deleteButton = StyleHelper.CreateStyledButton("حذف", StyleHelper.DangerColor,
                new Size(100, 40), DeleteOrder_Click);
            deleteButton.Location = new Point(50 + buttonSpacing * 2, buttonY);
        }

        private void CreateDataGrid()
        {
            ordersGrid = new DataGridView
            {
                Location = new Point(400, 120),
                Size = new Size(950, 600),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            StyleHelper.StyleDataGridView(ordersGrid);

            ordersGrid.SelectionChanged += (s, e) =>
            {
                if (ordersGrid.SelectedRows.Count > 0)
                {
                    var row = ordersGrid.SelectedRows[0];
                    selectedOrderId = (int)row.Cells["Id"].Value;
                    customerNameTextBox.Text = row.Cells["CustomerName"].Value?.ToString() ?? "";
                    customerPhoneTextBox.Text = row.Cells["CustomerPhone"].Value?.ToString() ?? "";
                    orderTypeTextBox.Text = row.Cells["OrderType"].Value?.ToString() ?? "";
                    totalPriceTextBox.Text = row.Cells["TotalPrice"].Value?.ToString() ?? "";
                    paymentTextBox.Text = row.Cells["Payment"].Value?.ToString() ?? "";
                    CalculateRemaining(null, null);
                }
            };
        }

        private void CalculateRemaining(object? sender, EventArgs? e)
        {
            if (decimal.TryParse(totalPriceTextBox.Text, out var total) &&
                decimal.TryParse(paymentTextBox.Text, out var payment))
            {
                var remaining = total - payment;
                remainingLabel.Text = $"المبلغ المتبقي: {remaining:F2}";
                remainingLabel.ForeColor = remaining > 0 ? StyleHelper.DangerColor : StyleHelper.SuccessColor;
            }
            else
            {
                remainingLabel.Text = "المبلغ المتبقي: 0";
                remainingLabel.ForeColor = StyleHelper.WarningColor;
            }
        }

        private async void LoadOrders()
        {
            try
            {
                using var context = new DatabaseContext();
                var orders = await context.Orders.OrderByDescending(o => o.CreatedAt).ToListAsync();

                ordersGrid.DataSource = orders.Select(o => new
                {
                    o.Id,
                    o.CustomerName,
                    o.CustomerPhone,
                    o.OrderType,
                    o.TotalPrice,
                    o.Payment,
                    Remaining = o.Remaining,
                    CreatedAt = o.CreatedAt.ToString("yyyy-MM-dd HH:mm"),
                    Status = o.IsCompleted ? "مكتمل" : "قيد التنفيذ"
                }).ToList();

                ordersGrid.Columns["Id"].Visible = false;
                ordersGrid.Columns["CustomerName"].HeaderText = "اسم العميل";
                ordersGrid.Columns["CustomerPhone"].HeaderText = "الهاتف";
                ordersGrid.Columns["OrderType"].HeaderText = "نوع الطلب";
                ordersGrid.Columns["TotalPrice"].HeaderText = "السعر الإجمالي";
                ordersGrid.Columns["Payment"].HeaderText = "المدفوع";
                ordersGrid.Columns["Remaining"].HeaderText = "المتبقي";
                ordersGrid.Columns["CreatedAt"].HeaderText = "تاريخ الإنشاء";
                ordersGrid.Columns["Status"].HeaderText = "الحالة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void AddOrder_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                await AnimationHelper.ScaleButton(addButton);

                using var context = new DatabaseContext();
                var order = new Order
                {
                    CustomerName = customerNameTextBox.Text.Trim(),
                    CustomerPhone = customerPhoneTextBox.Text.Trim(),
                    OrderType = orderTypeTextBox.Text.Trim(),
                    TotalPrice = decimal.Parse(totalPriceTextBox.Text.Trim()),
                    Payment = decimal.Parse(paymentTextBox.Text.Trim()),
                    CreatedAt = DateTime.Now,
                    IsCompleted = false
                };

                context.Orders.Add(order);
                await context.SaveChangesAsync();

                // Create debt if there's remaining amount
                if (order.Remaining > 0)
                {
                    var debt = new Debt
                    {
                        CustomerName = order.CustomerName,
                        CustomerPhone = order.CustomerPhone,
                        Amount = order.Remaining,
                        Description = $"دين من الطلب: {order.OrderType}",
                        CreatedAt = DateTime.Now,
                        OrderId = order.Id,
                        IsPaid = false
                    };

                    context.Debts.Add(debt);
                    await context.SaveChangesAsync();
                }

                ClearInputs();
                LoadOrders();

                MessageBox.Show("تم إضافة الطلب بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                await AnimationHelper.ShakeControl(addButton);
                MessageBox.Show($"خطأ في إضافة الطلب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void UpdateOrder_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedOrderId == -1)
                {
                    MessageBox.Show("يرجى اختيار طلب للتحديث", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!ValidateInput()) return;

                await AnimationHelper.ScaleButton(updateButton);

                using var context = new DatabaseContext();
                var order = await context.Orders.FindAsync(selectedOrderId);

                if (order != null)
                {
                    var oldRemaining = order.Remaining;

                    order.CustomerName = customerNameTextBox.Text.Trim();
                    order.CustomerPhone = customerPhoneTextBox.Text.Trim();
                    order.OrderType = orderTypeTextBox.Text.Trim();
                    order.TotalPrice = decimal.Parse(totalPriceTextBox.Text.Trim());
                    order.Payment = decimal.Parse(paymentTextBox.Text.Trim());

                    await context.SaveChangesAsync();

                    // Update related debt
                    var debt = await context.Debts.FirstOrDefaultAsync(d => d.OrderId == selectedOrderId);
                    if (debt != null)
                    {
                        if (order.Remaining > 0)
                        {
                            debt.Amount = order.Remaining;
                            debt.CustomerName = order.CustomerName;
                            debt.CustomerPhone = order.CustomerPhone;
                        }
                        else
                        {
                            debt.IsPaid = true;
                            debt.PaidAt = DateTime.Now;
                        }
                        await context.SaveChangesAsync();
                    }
                    else if (order.Remaining > 0)
                    {
                        // Create new debt if none exists
                        var newDebt = new Debt
                        {
                            CustomerName = order.CustomerName,
                            CustomerPhone = order.CustomerPhone,
                            Amount = order.Remaining,
                            Description = $"دين من الطلب: {order.OrderType}",
                            CreatedAt = DateTime.Now,
                            OrderId = order.Id,
                            IsPaid = false
                        };
                        context.Debts.Add(newDebt);
                        await context.SaveChangesAsync();
                    }

                    ClearInputs();
                    LoadOrders();

                    MessageBox.Show("تم تحديث الطلب بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                await AnimationHelper.ShakeControl(updateButton);
                MessageBox.Show($"خطأ في تحديث الطلب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteOrder_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedOrderId == -1)
                {
                    MessageBox.Show("يرجى اختيار طلب للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف هذا الطلب؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await AnimationHelper.ScaleButton(deleteButton);

                    using var context = new DatabaseContext();
                    var order = await context.Orders.FindAsync(selectedOrderId);

                    if (order != null)
                    {
                        // Delete related debt
                        var debt = await context.Debts.FirstOrDefaultAsync(d => d.OrderId == selectedOrderId);
                        if (debt != null)
                        {
                            context.Debts.Remove(debt);
                        }

                        context.Orders.Remove(order);
                        await context.SaveChangesAsync();

                        ClearInputs();
                        LoadOrders();

                        MessageBox.Show("تم حذف الطلب بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                await AnimationHelper.ShakeControl(deleteButton);
                MessageBox.Show($"خطأ في حذف الطلب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(customerNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                customerNameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(orderTypeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال نوع الطلب", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                orderTypeTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(totalPriceTextBox.Text) ||
                !decimal.TryParse(totalPriceTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال سعر صحيح", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                totalPriceTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(paymentTextBox.Text) ||
                !decimal.TryParse(paymentTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال مبلغ مدفوع صحيح", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                paymentTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearInputs()
        {
            customerNameTextBox.Text = "";
            customerPhoneTextBox.Text = "";
            orderTypeTextBox.Text = "";
            totalPriceTextBox.Text = "";
            paymentTextBox.Text = "";
            selectedOrderId = -1;
            CalculateRemaining(null, null);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                this.Close();
            }
            base.OnKeyDown(e);
        }
    }
}
