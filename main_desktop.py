#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - التطبيق الرئيسي للسطح المكتب
Ma'aris Al Jahra - Main Desktop Application

تطبيق سطح مكتب سريع ومتجاوب لإدارة العملاء والطلبات والديون
Fast and responsive desktop application for managing customers, orders, and debts
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PySide6.QtWidgets import QApplication, QMessageBox
    from PySide6.QtCore import Qt
    from PySide6.QtGui import QFont, QIcon
    PYSIDE6_AVAILABLE = True
except ImportError:
    print("خطأ: PySide6 غير مثبت")
    print("Error: PySide6 is not installed")
    print("يرجى تثبيت PySide6 باستخدام: pip install PySide6")
    print("Please install PySide6 using: pip install PySide6")
    sys.exit(1)

from ui.base_interface import BaseInterface
from database import Database

class MaarisAlJahraApp(BaseInterface):
    """
    التطبيق الرئيسي لمعاريس الجهراء
    Main application for Ma'aris Al Jahra
    """
    
    def __init__(self):
        super().__init__("مدير النظام")
        
        # Initialize data on startup
        self.initialize_data()
        
        # Set application icon if available
        self.set_app_icon()
        
        # Show welcome message
        self.show_welcome_message()
    
    def initialize_data(self):
        """Initialize application data."""
        try:
            # Load customers
            self.search_customers()
            
            # Load customers for orders
            self.search_customers_for_order()
            
            # Load reports
            self.refresh_order_statistics()
            
            print("✅ تم تحميل البيانات بنجاح")
            print("✅ Data loaded successfully")
            
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            print(f"❌ Error loading data: {e}")
    
    def set_app_icon(self):
        """Set application icon if available."""
        try:
            # Try to set an icon if available
            icon_path = "assets/icon.png"
            if os.path.exists(icon_path):
                self.setWindowIcon(QIcon(icon_path))
        except Exception:
            # Icon not available, continue without it
            pass
    
    def show_welcome_message(self):
        """Show welcome message to user."""
        welcome_msg = """
🎉 مرحباً بك في معاريس الجهراء 🎉

✨ تطبيق سطح المكتب السريع والمتجاوب ✨

🚀 المميزات:
• إدارة العملاء بسرعة فائقة
• إضافة وحذف الطلبات فوراً
• ربط تلقائي بين الطلبات والديون
• واجهة عربية جميلة ومريحة

💡 نصائح للاستخدام:
• استخدم البحث للعثور على العملاء بسرعة
• اضغط على أزرار الحذف للحذف الفوري
• جميع البيانات محفوظة محلياً وآمنة

🎯 جاهز للاستخدام!
        """
        
        QMessageBox.information(
            self, 
            "معاريس الجهراء - مرحباً بك", 
            welcome_msg
        )
    
    def go_back(self):
        """Handle back button - close application."""
        reply = QMessageBox.question(
            self,
            "إغلاق التطبيق",
            "هل تريد إغلاق التطبيق؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close()

def main():
    """Main function to run the application."""
    
    # Create QApplication
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("معاريس الجهراء")
    app.setApplicationDisplayName("معاريس الجهراء - نظام إدارة العملاء والطلبات")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("معاريس الجهراء")
    
    # Set default font for Arabic text
    font = QFont("Traditional Arabic", 12)
    app.setFont(font)
    
    # Set RTL layout for Arabic
    app.setLayoutDirection(Qt.RightToLeft)
    
    # Create and show main window
    try:
        print("🚀 بدء تشغيل معاريس الجهراء...")
        print("🚀 Starting Ma'aris Al Jahra...")
        
        # Test database connection
        db = Database()
        print("✅ اتصال قاعدة البيانات ناجح")
        print("✅ Database connection successful")
        
        # Create main window
        window = MaarisAlJahraApp()
        window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح")
        print("✅ Application started successfully")
        print("🎯 التطبيق جاهز للاستخدام!")
        print("🎯 Application ready to use!")
        
        # Run application
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print(f"❌ Error starting application: {e}")
        
        # Show error message
        error_app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
        QMessageBox.critical(
            None,
            "خطأ في التطبيق",
            f"حدث خطأ في تشغيل التطبيق:\n\n{str(e)}\n\nيرجى التأكد من تثبيت جميع المتطلبات."
        )
        sys.exit(1)

if __name__ == "__main__":
    main()
