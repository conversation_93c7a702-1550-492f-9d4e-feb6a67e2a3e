#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - التطبيق الرئيسي للسطح المكتب
Ma'aris Al Jahra - Main Desktop Application

تطبيق سطح مكتب سريع ومتجاوب لإدارة العملاء والطلبات والديون
Fast and responsive desktop application for managing customers, orders, and debts
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PySide6.QtWidgets import QApplication, QMessageBox
    from PySide6.QtCore import Qt
    from PySide6.QtGui import QFont
    PYSIDE6_AVAILABLE = True
except ImportError as e:
    print("خطأ: PySide6 غير مثبت")
    print("Error: PySide6 is not installed")
    print("يرجى تثبيت PySide6 باستخدام: pip install PySide6")
    print("Please install PySide6 using: pip install PySide6")
    print(f"Import error details: {e}")
    sys.exit(1)

from ui.beautiful_interface import BeautifulInterface
from database import Database

class MaarisAlJahraApp(BeautifulInterface):
    """
    التطبيق الرئيسي لمعاريس الجهراء
    Main application for Ma'aris Al Jahra
    """

    def __init__(self):
        super().__init__()

        # Show welcome message
        self.show_welcome_message()

    def show_welcome_message(self):
        """Show welcome message to user."""
        welcome_msg = """
🎉 مرحباً بك في معاريس الجهراء 🎉

✨ تطبيق سطح المكتب الجميل والمتطور ✨

🚀 المميزات الجديدة:
• واجهة جميلة بألوان متدرجة وتصميم عصري
• إدارة العملاء بسرعة فائقة مع البحث الفوري
• إضافة وحذف الطلبات فوراً مع تأكيد جميل
• ربط تلقائي بين الطلبات والديون
• تقارير يومية وشهرية تفاعلية
• إحصائيات مباشرة في الرأس

💡 نصائح للاستخدام:
• استخدم البحث للعثور على العملاء بسرعة
• اضغط على أزرار الحذف للحذف الفوري
• جميع البيانات محفوظة محلياً وآمنة
• التطبيق يحدث البيانات تلقائياً كل 30 ثانية

🎯 جاهز للاستخدام!
        """

        QMessageBox.information(
            self,
            "معاريس الجهراء - مرحباً بك",
            welcome_msg
        )

def main():
    """Main function to run the application."""

    # Create QApplication
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("معاريس الجهراء")
    app.setApplicationDisplayName("معاريس الجهراء - نظام إدارة العملاء والطلبات")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("معاريس الجهراء")

    # Set default font for Arabic text
    font = QFont("Traditional Arabic", 12)
    app.setFont(font)

    # Set RTL layout for Arabic
    app.setLayoutDirection(Qt.RightToLeft)

    # Create and show main window
    try:
        print("🚀 بدء تشغيل معاريس الجهراء...")
        print("🚀 Starting Ma'aris Al Jahra...")

        # Test database connection
        try:
            db = Database()
            # Test basic database functionality
            _ = db.get_all_customers()
            print("✅ اتصال قاعدة البيانات ناجح")
            print("✅ Database connection successful")
        except Exception as db_error:
            print(f"❌ خطأ في قاعدة البيانات: {db_error}")
            print(f"❌ Database error: {db_error}")
            raise

        # Create main window
        window = MaarisAlJahraApp()
        window.show()

        print("✅ تم تشغيل التطبيق بنجاح")
        print("✅ Application started successfully")
        print("🎯 التطبيق جاهز للاستخدام!")
        print("🎯 Application ready to use!")

        # Run application
        sys.exit(app.exec())

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print(f"❌ Error starting application: {e}")

        # Show error message
        if not QApplication.instance():
            QApplication(sys.argv)
        QMessageBox.critical(
            None,
            "خطأ في التطبيق",
            f"حدث خطأ في تشغيل التطبيق:\n\n{str(e)}\n\nيرجى التأكد من تثبيت جميع المتطلبات."
        )
        sys.exit(1)

if __name__ == "__main__":
    main()
