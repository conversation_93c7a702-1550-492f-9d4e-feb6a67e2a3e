import React from 'react';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import { 
  Users, 
  ShoppingCart, 
  CreditCard, 
  TrendingUp,
  Calendar,
  DollarSign
} from 'lucide-react';
import { fetchDailyStatistics, fetchMonthlyStatistics } from '../services/api';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  delay: number;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, delay }) => (
  <motion.div
    className={`card hover-lift ${color}`}
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5, delay }}
    whileHover={{ scale: 1.02 }}
  >
    <div className="flex items-center justify-between">
      <div>
        <p className="text-white text-sm opacity-80">{title}</p>
        <p className="text-white text-2xl font-bold mt-1">{value}</p>
      </div>
      <div className="bg-white bg-opacity-20 p-3 rounded-full">
        {icon}
      </div>
    </div>
  </motion.div>
);

const Dashboard: React.FC = () => {
  const { data: dailyStats, isLoading: dailyLoading } = useQuery(
    'dailyStatistics',
    fetchDailyStatistics,
    { refetchInterval: 30000 } // Refresh every 30 seconds
  );

  const { data: monthlyStats, isLoading: monthlyLoading } = useQuery(
    'monthlyStatistics',
    fetchMonthlyStatistics,
    { refetchInterval: 30000 }
  );

  if (dailyLoading || monthlyLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-3xl font-bold text-gray-800 mb-2">لوحة التحكم</h1>
        <p className="text-gray-600">نظرة عامة على أداء النشاط التجاري</p>
      </motion.div>

      {/* Today's Statistics */}
      <motion.section
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <Calendar className="w-5 h-5 ml-2" />
          إحصائيات اليوم
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="عدد الطلبات"
            value={dailyStats?.total_orders || 0}
            icon={<ShoppingCart className="w-6 h-6 text-white" />}
            color="bg-gradient-to-r from-blue-500 to-blue-600"
            delay={0.1}
          />
          <StatCard
            title="إجمالي المبيعات"
            value={`${dailyStats?.total_amount || 0} د.ك`}
            icon={<DollarSign className="w-6 h-6 text-white" />}
            color="bg-gradient-to-r from-green-500 to-green-600"
            delay={0.2}
          />
          <StatCard
            title="المبلغ المدفوع"
            value={`${dailyStats?.paid_amount || 0} د.ك`}
            icon={<TrendingUp className="w-6 h-6 text-white" />}
            color="bg-gradient-to-r from-primary-400 to-primary-600"
            delay={0.3}
          />
          <StatCard
            title="المبلغ المتبقي"
            value={`${dailyStats?.remaining_amount || 0} د.ك`}
            icon={<CreditCard className="w-6 h-6 text-white" />}
            color="bg-gradient-to-r from-red-500 to-red-600"
            delay={0.4}
          />
        </div>
      </motion.section>

      {/* Monthly Statistics */}
      <motion.section
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <Calendar className="w-5 h-5 ml-2" />
          إحصائيات الشهر
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="عدد الطلبات"
            value={monthlyStats?.total_orders || 0}
            icon={<ShoppingCart className="w-6 h-6 text-white" />}
            color="bg-gradient-to-r from-purple-500 to-purple-600"
            delay={0.5}
          />
          <StatCard
            title="إجمالي المبيعات"
            value={`${monthlyStats?.total_amount || 0} د.ك`}
            icon={<DollarSign className="w-6 h-6 text-white" />}
            color="bg-gradient-to-r from-indigo-500 to-indigo-600"
            delay={0.6}
          />
          <StatCard
            title="المبلغ المدفوع"
            value={`${monthlyStats?.paid_amount || 0} د.ك`}
            icon={<TrendingUp className="w-6 h-6 text-white" />}
            color="bg-gradient-to-r from-pink-500 to-pink-600"
            delay={0.7}
          />
          <StatCard
            title="المبلغ المتبقي"
            value={`${monthlyStats?.remaining_amount || 0} د.ك`}
            icon={<CreditCard className="w-6 h-6 text-white" />}
            color="bg-gradient-to-r from-orange-500 to-orange-600"
            delay={0.8}
          />
        </div>
      </motion.section>

      {/* Quick Actions */}
      <motion.section
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.6 }}
      >
        <h2 className="text-xl font-semibold text-gray-800 mb-4">إجراءات سريعة</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <motion.div
            className="card hover-lift cursor-pointer"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="text-center">
              <Users className="w-12 h-12 text-primary-500 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">إضافة عميل جديد</h3>
              <p className="text-gray-600 text-sm">إضافة عميل جديد مع قياساته</p>
            </div>
          </motion.div>

          <motion.div
            className="card hover-lift cursor-pointer"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="text-center">
              <ShoppingCart className="w-12 h-12 text-secondary-500 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">إنشاء طلب جديد</h3>
              <p className="text-gray-600 text-sm">إضافة طلب جديد لعميل موجود</p>
            </div>
          </motion.div>

          <motion.div
            className="card hover-lift cursor-pointer"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="text-center">
              <CreditCard className="w-12 h-12 text-red-500 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">إدارة الديون</h3>
              <p className="text-gray-600 text-sm">متابعة وإدارة ديون العملاء</p>
            </div>
          </motion.div>
        </div>
      </motion.section>
    </div>
  );
};

export default Dashboard;
