using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;

namespace MaarisAlJahra.Helpers
{
    public static class IconGenerator
    {
        public static Icon CreateApplicationIcon()
        {
            // Create a 256x256 bitmap for high quality
            using (var bitmap = new Bitmap(256, 256))
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;

                // Background gradient (elegant blue to gold)
                using (var backgroundBrush = new LinearGradientBrush(
                    new Rectangle(0, 0, 256, 256),
                    Color.FromArgb(41, 128, 185),  // Beautiful blue
                    Color.FromArgb(241, 196, 15),  // Gold
                    LinearGradientMode.Vertical))
                {
                    graphics.FillEllipse(backgroundBrush, 10, 10, 236, 236);
                }

                // Outer border (silver)
                using (var borderPen = new Pen(Color.FromArgb(149, 165, 166), 4))
                {
                    graphics.DrawEllipse(borderPen, 10, 10, 236, 236);
                }

                // Draw scissors (main element)
                DrawScissors(graphics);

                // Draw thread around scissors
                DrawThread(graphics);

                // Draw Arabic text "معاريس الجهراء"
                DrawArabicText(graphics);

                // Inner glow effect
                using (var glowPen = new Pen(Color.FromArgb(100, 255, 255, 255), 2))
                {
                    graphics.DrawEllipse(glowPen, 15, 15, 226, 226);
                }

                return Icon.FromHandle(bitmap.GetHicon());
            }
        }

        private static void DrawScissors(Graphics graphics)
        {
            // Scissors color (silver/metallic)
            var scissorsColor = Color.FromArgb(192, 192, 192);
            var handleColor = Color.FromArgb(139, 69, 19); // Brown handles

            using (var scissorsPen = new Pen(scissorsColor, 6))
            using (var handleBrush = new SolidBrush(handleColor))
            {
                // Left blade
                graphics.DrawLine(scissorsPen, 80, 100, 120, 140);
                graphics.DrawLine(scissorsPen, 80, 100, 110, 90);
                graphics.DrawLine(scissorsPen, 110, 90, 120, 140);

                // Right blade  
                graphics.DrawLine(scissorsPen, 176, 100, 136, 140);
                graphics.DrawLine(scissorsPen, 176, 100, 146, 90);
                graphics.DrawLine(scissorsPen, 146, 90, 136, 140);

                // Pivot point
                graphics.FillEllipse(new SolidBrush(scissorsColor), 124, 116, 8, 8);

                // Left handle (ring)
                graphics.FillEllipse(handleBrush, 70, 150, 25, 25);
                graphics.FillEllipse(new SolidBrush(Color.FromArgb(160, 82, 45)), 75, 155, 15, 15);

                // Right handle (ring)
                graphics.FillEllipse(handleBrush, 161, 150, 25, 25);
                graphics.FillEllipse(new SolidBrush(Color.FromArgb(160, 82, 45)), 166, 155, 15, 15);
            }
        }

        private static void DrawThread(Graphics graphics)
        {
            // Thread color (golden thread)
            var threadColor = Color.FromArgb(255, 215, 0);
            
            using (var threadPen = new Pen(threadColor, 3))
            {
                // Create curved thread path around scissors
                var points = new PointF[]
                {
                    new PointF(60, 120),
                    new PointF(50, 100),
                    new PointF(70, 80),
                    new PointF(100, 70),
                    new PointF(130, 75),
                    new PointF(160, 80),
                    new PointF(190, 100),
                    new PointF(200, 120),
                    new PointF(190, 140),
                    new PointF(170, 160),
                    new PointF(140, 170),
                    new PointF(110, 165),
                    new PointF(80, 160),
                    new PointF(60, 140)
                };

                // Draw smooth curved thread
                graphics.DrawCurve(threadPen, points, 0.5f);
                
                // Add thread texture with small lines
                using (var texturePen = new Pen(Color.FromArgb(218, 165, 32), 1))
                {
                    for (int i = 0; i < points.Length - 1; i++)
                    {
                        var p1 = points[i];
                        var p2 = points[i + 1];
                        var midX = (p1.X + p2.X) / 2;
                        var midY = (p1.Y + p2.Y) / 2;
                        graphics.DrawLine(texturePen, midX - 2, midY - 2, midX + 2, midY + 2);
                    }
                }
            }
        }

        private static void DrawArabicText(Graphics graphics)
        {
            // Arabic text "معاريس الجهراء"
            var arabicText = "معاريس الجهراء";
            var font = new Font("Arial", 16, FontStyle.Bold);
            var textColor = Color.White;
            var shadowColor = Color.FromArgb(100, 0, 0, 0);

            // Measure text
            var textSize = graphics.MeasureString(arabicText, font);
            var x = (256 - textSize.Width) / 2;
            var y = 200; // Bottom area

            // Draw text shadow
            using (var shadowBrush = new SolidBrush(shadowColor))
            {
                graphics.DrawString(arabicText, font, shadowBrush, x + 2, y + 2);
            }

            // Draw main text
            using (var textBrush = new SolidBrush(textColor))
            {
                graphics.DrawString(arabicText, font, textBrush, x, y);
            }

            // Add text outline for better visibility
            using (var outlinePen = new Pen(Color.FromArgb(44, 62, 80), 1))
            {
                var path = new GraphicsPath();
                path.AddString(arabicText, font.FontFamily, (int)font.Style, font.Size, new PointF(x, y), StringFormat.GenericDefault);
                graphics.DrawPath(outlinePen, path);
            }
        }

        public static void SaveIconToFile(string filePath)
        {
            var icon = CreateApplicationIcon();
            using (var fileStream = new FileStream(filePath, FileMode.Create))
            {
                icon.Save(fileStream);
            }
        }

        // Create multiple icon sizes for Windows
        public static void CreateIconFile(string filePath)
        {
            var sizes = new int[] { 16, 32, 48, 64, 128, 256 };
            var bitmaps = new Bitmap[sizes.Length];

            for (int i = 0; i < sizes.Length; i++)
            {
                bitmaps[i] = CreateIconBitmap(sizes[i]);
            }

            // Save as ICO file with multiple sizes
            SaveAsIcoFile(filePath, bitmaps);

            // Dispose bitmaps
            foreach (var bitmap in bitmaps)
            {
                bitmap?.Dispose();
            }
        }

        private static Bitmap CreateIconBitmap(int size)
        {
            var bitmap = new Bitmap(size, size);
            using (var graphics = Graphics.FromImage(bitmap))
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;

                // Scale the original 256x256 design to the target size
                using (var originalBitmap = new Bitmap(256, 256))
                using (var originalGraphics = Graphics.FromImage(originalBitmap))
                {
                    // Draw the full icon design on original bitmap
                    DrawFullIcon(originalGraphics);
                    
                    // Scale down to target size
                    graphics.DrawImage(originalBitmap, 0, 0, size, size);
                }
            }
            return bitmap;
        }

        private static void DrawFullIcon(Graphics graphics)
        {
            graphics.SmoothingMode = SmoothingMode.AntiAlias;
            
            // Background gradient
            using (var backgroundBrush = new LinearGradientBrush(
                new Rectangle(0, 0, 256, 256),
                Color.FromArgb(41, 128, 185),
                Color.FromArgb(241, 196, 15),
                LinearGradientMode.Vertical))
            {
                graphics.FillEllipse(backgroundBrush, 10, 10, 236, 236);
            }

            DrawScissors(graphics);
            DrawThread(graphics);
            DrawArabicText(graphics);
        }

        private static void SaveAsIcoFile(string filePath, Bitmap[] bitmaps)
        {
            using (var fileStream = new FileStream(filePath, FileMode.Create))
            using (var writer = new BinaryWriter(fileStream))
            {
                // ICO header
                writer.Write((short)0);  // Reserved
                writer.Write((short)1);  // Type (1 = ICO)
                writer.Write((short)bitmaps.Length);  // Number of images

                var offset = 6 + (16 * bitmaps.Length);

                // Directory entries
                foreach (var bitmap in bitmaps)
                {
                    writer.Write((byte)bitmap.Width);
                    writer.Write((byte)bitmap.Height);
                    writer.Write((byte)0);  // Color count
                    writer.Write((byte)0);  // Reserved
                    writer.Write((short)1); // Planes
                    writer.Write((short)32); // Bits per pixel
                    
                    using (var ms = new MemoryStream())
                    {
                        bitmap.Save(ms, ImageFormat.Png);
                        var imageData = ms.ToArray();
                        writer.Write(imageData.Length);
                        writer.Write(offset);
                        offset += imageData.Length;
                    }
                }

                // Image data
                foreach (var bitmap in bitmaps)
                {
                    using (var ms = new MemoryStream())
                    {
                        bitmap.Save(ms, ImageFormat.Png);
                        writer.Write(ms.ToArray());
                    }
                }
            }
        }
    }
}
