#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ma'aris <PERSON> - Database Module
"""

import os
import json
import sqlite3
from datetime import datetime

class Database:
    def __init__(self, db_file="maaris_aljahra.db"):
        self.db_file = db_file
        self.conn = None
        self.cursor = None
        self.initialize()

    def initialize(self):
        """Initialize the database connection and create tables if they don't exist."""
        self.conn = sqlite3.connect(self.db_file)
        self.cursor = self.conn.cursor()

        # Create customers table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            chest REAL,
            shoulder REAL,
            hand REAL,
            waist REAL,
            hip REAL,
            inseam REAL,
            neck REAL,
            height REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create orders table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER,
            order_type TEXT,
            total_price REAL,
            payment REAL,
            remaining REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id)
        )
        ''')

        # Create debts table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS debts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER,
            total_due REAL,
            paid REAL,
            remaining REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id)
        )
        ''')

        self.conn.commit()

    def close(self):
        """Close the database connection."""
        if self.conn:
            self.conn.close()

    # Customer operations
    def add_customer(self, name, phone, chest=0, shoulder=0, hand=0, waist=0, hip=0, inseam=0, neck=0, height=0):
        """Add a new customer to the database."""
        self.cursor.execute('''
        INSERT INTO customers (name, phone, chest, shoulder, hand, waist, hip, inseam, neck, height)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (name, phone, chest, shoulder, hand, waist, hip, inseam, neck, height))
        self.conn.commit()
        return self.cursor.lastrowid

    def get_customer(self, customer_id):
        """Get a customer by ID."""
        self.cursor.execute('SELECT * FROM customers WHERE id = ?', (customer_id,))
        return self.cursor.fetchone()

    def search_customers(self, search_term):
        """Search customers by name or phone."""
        self.cursor.execute('''
        SELECT * FROM customers
        WHERE name LIKE ? OR phone LIKE ?
        ''', (f'%{search_term}%', f'%{search_term}%'))
        return self.cursor.fetchall()

    def update_customer(self, customer_id, name, phone, chest=0, shoulder=0, hand=0, waist=0, hip=0, inseam=0, neck=0, height=0):
        """Update customer information."""
        self.cursor.execute('''
        UPDATE customers
        SET name = ?, phone = ?, chest = ?, shoulder = ?, hand = ?, waist = ?, hip = ?, inseam = ?, neck = ?, height = ?
        WHERE id = ?
        ''', (name, phone, chest, shoulder, hand, waist, hip, inseam, neck, height, customer_id))
        self.conn.commit()
        return self.cursor.rowcount

    def delete_customer(self, customer_id):
        """Delete a customer by ID."""
        self.cursor.execute('DELETE FROM customers WHERE id = ?', (customer_id,))
        self.conn.commit()
        return self.cursor.rowcount

    def get_all_customers(self):
        """Retrieve all customers from the database."""
        self.cursor.execute('SELECT * FROM customers')
        return self.cursor.fetchall()

    # Order operations
    def add_order(self, customer_id, order_type, total_price, payment):
        """Add a new order."""
        remaining = total_price - payment
        self.cursor.execute('''
        INSERT INTO orders (customer_id, order_type, total_price, payment, remaining)
        VALUES (?, ?, ?, ?, ?)
        ''', (customer_id, order_type, total_price, payment, remaining))
        self.conn.commit()
        return self.cursor.lastrowid

    def get_customer_orders(self, customer_id):
        """Get all orders for a customer."""
        self.cursor.execute('SELECT * FROM orders WHERE customer_id = ?', (customer_id,))
        return self.cursor.fetchall()

    def update_order(self, order_id, order_type, total_price, payment):
        """Update an order."""
        remaining = total_price - payment
        self.cursor.execute('''
        UPDATE orders
        SET order_type = ?, total_price = ?, payment = ?, remaining = ?
        WHERE id = ?
        ''', (order_type, total_price, payment, remaining, order_id))
        self.conn.commit()
        return self.cursor.rowcount

    # Debt operations
    def add_debt(self, customer_id, total_due, paid):
        """Add a new debt record."""
        remaining = total_due - paid
        self.cursor.execute('''
        INSERT INTO debts (customer_id, total_due, paid, remaining)
        VALUES (?, ?, ?, ?)
        ''', (customer_id, total_due, paid, remaining))
        self.conn.commit()
        return self.cursor.lastrowid

    def get_customer_debts(self, customer_id):
        """Get all debt records for a customer."""
        self.cursor.execute('SELECT * FROM debts WHERE customer_id = ?', (customer_id,))
        return self.cursor.fetchall()

    def update_debt(self, debt_id, total_due, paid):
        """Update a debt record."""
        remaining = total_due - paid
        self.cursor.execute('''
        UPDATE debts
        SET total_due = ?, paid = ?, remaining = ?
        WHERE id = ?
        ''', (total_due, paid, remaining, debt_id))
        self.conn.commit()
        return self.cursor.rowcount

    def get_customers_with_debt(self):
        """Get all customers who have outstanding debt."""
        self.cursor.execute('''
        SELECT c.* FROM customers c
        JOIN debts d ON c.id = d.customer_id
        WHERE d.remaining > 0
        GROUP BY c.id
        ''')
        return self.cursor.fetchall()

    # Order statistics and reporting
    def get_daily_orders_total(self):
        """Get the total amount of orders for the current day."""
        today = datetime.now().strftime('%Y-%m-%d')
        self.cursor.execute('''
        SELECT SUM(total_price) as total, SUM(payment) as paid, SUM(remaining) as remaining
        FROM orders
        WHERE date(created_at) = ?
        ''', (today,))
        result = self.cursor.fetchone()
        if result and result[0]:
            return {
                'total': result[0],
                'paid': result[1],
                'remaining': result[2]
            }
        return {'total': 0, 'paid': 0, 'remaining': 0}

    def get_monthly_orders_total(self):
        """Get the total amount of orders for the current month."""
        current_month = datetime.now().strftime('%Y-%m')
        self.cursor.execute('''
        SELECT SUM(total_price) as total, SUM(payment) as paid, SUM(remaining) as remaining
        FROM orders
        WHERE strftime('%Y-%m', created_at) = ?
        ''', (current_month,))
        result = self.cursor.fetchone()
        if result and result[0]:
            return {
                'total': result[0],
                'paid': result[1],
                'remaining': result[2]
            }
        return {'total': 0, 'paid': 0, 'remaining': 0}

    def get_order(self, order_id):
        """Get an order by ID."""
        self.cursor.execute('''
        SELECT o.*, c.name as customer_name, c.phone as customer_phone
        FROM orders o
        JOIN customers c ON o.customer_id = c.id
        WHERE o.id = ?
        ''', (order_id,))
        return self.cursor.fetchone()

    def get_all_orders(self):
        """Get all orders with customer information."""
        self.cursor.execute('''
        SELECT o.*, c.name as customer_name, c.phone as customer_phone
        FROM orders o
        JOIN customers c ON o.customer_id = c.id
        ORDER BY o.created_at DESC
        ''')
        return self.cursor.fetchall()

    def delete_order(self, order_id):
        """Delete an order by ID."""
        self.cursor.execute('DELETE FROM orders WHERE id = ?', (order_id,))
        self.conn.commit()
        return self.cursor.rowcount
