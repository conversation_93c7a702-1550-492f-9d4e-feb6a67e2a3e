#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ma'aris <PERSON> - Database Module
"""

import os
import json
import sqlite3
from datetime import datetime

class Database:
    def __init__(self, db_file="maaris_aljahra.db"):
        self.db_file = db_file
        self.conn = None
        self.cursor = None
        self.initialize()

    def initialize(self):
        """Initialize the database connection and create tables if they don't exist."""
        self.conn = sqlite3.connect(self.db_file)
        self.cursor = self.conn.cursor()

        # Create customers table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            phone TEXT,
            chest REAL,
            shoulder REAL,
            hand REAL,
            waist REAL,
            hip REAL,
            inseam REAL,
            neck REAL,
            height REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Create orders table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER,
            order_type TEXT,
            total_price REAL,
            payment REAL,
            remaining REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id)
        )
        ''')

        # Create debts table
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS debts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER,
            total_due REAL,
            paid REAL,
            remaining REAL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers (id)
        )
        ''')

        self.conn.commit()

    def close(self):
        """Close the database connection."""
        if self.conn:
            self.conn.close()

    # Customer operations
    def add_customer(self, customer_data):
        """Add a new customer to the database."""
        self.cursor.execute('''
        INSERT INTO customers (name, phone, chest, shoulder, hand, waist, hip, inseam, neck, height)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            customer_data['name'],
            customer_data['phone'],
            customer_data.get('chest', 0),
            customer_data.get('shoulder', 0),
            customer_data.get('hand', 0),
            customer_data.get('waist', 0),
            customer_data.get('hip', 0),
            customer_data.get('inseam', 0),
            customer_data.get('neck', 0),
            customer_data.get('height', 0)
        ))
        self.conn.commit()
        return self.cursor.lastrowid

    def get_customer(self, customer_id):
        """Get a customer by ID."""
        self.cursor.execute('SELECT * FROM customers WHERE id = ?', (customer_id,))
        row = self.cursor.fetchone()
        if row:
            return {
                'id': row[0], 'name': row[1], 'phone': row[2], 'chest': row[3],
                'shoulder': row[4], 'hand': row[5], 'waist': row[6], 'hip': row[7],
                'inseam': row[8], 'neck': row[9], 'height': row[10], 'created_at': row[11]
            }
        return None

    def search_customers(self, search_term):
        """Search customers by name or phone."""
        self.cursor.execute('''
        SELECT * FROM customers
        WHERE name LIKE ? OR phone LIKE ?
        ORDER BY name
        ''', (f'%{search_term}%', f'%{search_term}%'))
        rows = self.cursor.fetchall()
        return [
            {
                'id': row[0], 'name': row[1], 'phone': row[2], 'chest': row[3],
                'shoulder': row[4], 'hand': row[5], 'waist': row[6], 'hip': row[7],
                'inseam': row[8], 'neck': row[9], 'height': row[10], 'created_at': row[11]
            }
            for row in rows
        ]

    def update_customer(self, customer_data):
        """Update customer information."""
        self.cursor.execute('''
        UPDATE customers
        SET name = ?, phone = ?, chest = ?, shoulder = ?, hand = ?, waist = ?, hip = ?, inseam = ?, neck = ?, height = ?
        WHERE id = ?
        ''', (
            customer_data['name'], customer_data['phone'],
            customer_data.get('chest', 0), customer_data.get('shoulder', 0),
            customer_data.get('hand', 0), customer_data.get('waist', 0),
            customer_data.get('hip', 0), customer_data.get('inseam', 0),
            customer_data.get('neck', 0), customer_data.get('height', 0),
            customer_data['id']
        ))
        self.conn.commit()
        return self.cursor.rowcount

    def delete_customer(self, customer_id):
        """Delete a customer and all related orders and debts."""
        # Delete related orders first
        self.cursor.execute('DELETE FROM orders WHERE customer_id = ?', (customer_id,))
        # Delete related debts
        self.cursor.execute('DELETE FROM debts WHERE customer_id = ?', (customer_id,))
        # Delete customer
        self.cursor.execute('DELETE FROM customers WHERE id = ?', (customer_id,))
        self.conn.commit()
        return self.cursor.rowcount

    def get_all_customers(self):
        """Retrieve all customers from the database."""
        self.cursor.execute('SELECT * FROM customers ORDER BY name')
        rows = self.cursor.fetchall()
        return [
            {
                'id': row[0], 'name': row[1], 'phone': row[2], 'chest': row[3],
                'shoulder': row[4], 'hand': row[5], 'waist': row[6], 'hip': row[7],
                'inseam': row[8], 'neck': row[9], 'height': row[10], 'created_at': row[11]
            }
            for row in rows
        ]

    # Order operations
    def add_order(self, order_data):
        """Add a new order and create debt if needed."""
        remaining = order_data['total_price'] - order_data['payment']

        # Add order
        self.cursor.execute('''
        INSERT INTO orders (customer_id, order_type, total_price, payment, remaining)
        VALUES (?, ?, ?, ?, ?)
        ''', (order_data['customer_id'], order_data['order_type'],
              order_data['total_price'], order_data['payment'], remaining))

        order_id = self.cursor.lastrowid

        # Create debt if there's remaining amount
        if remaining > 0:
            self.add_debt(order_data['customer_id'], remaining, 0)

        self.conn.commit()
        return order_id

    def get_customer_orders(self, customer_id):
        """Get all orders for a customer."""
        self.cursor.execute('SELECT * FROM orders WHERE customer_id = ? ORDER BY created_at DESC', (customer_id,))
        rows = self.cursor.fetchall()
        return [
            {
                'id': row[0], 'customer_id': row[1], 'order_type': row[2],
                'total_price': row[3], 'payment': row[4],
                'remaining': row[3] - row[4],  # Calculate remaining
                'created_at': row[5]  # Adjusted index
            }
            for row in rows
        ]

    def update_order(self, order_data):
        """Update an order."""
        remaining = order_data['total_price'] - order_data['payment']
        self.cursor.execute('''
        UPDATE orders
        SET order_type = ?, total_price = ?, payment = ?, remaining = ?
        WHERE id = ?
        ''', (order_data['order_type'], order_data['total_price'],
              order_data['payment'], remaining, order_data['id']))
        self.conn.commit()
        return self.cursor.rowcount

    # Debt operations
    def add_debt(self, customer_id, total_due, paid):
        """Add a new debt record."""
        remaining = total_due - paid
        self.cursor.execute('''
        INSERT INTO debts (customer_id, total_due, paid, remaining)
        VALUES (?, ?, ?, ?)
        ''', (customer_id, total_due, paid, remaining))
        self.conn.commit()
        return self.cursor.lastrowid

    def get_customer_debts(self, customer_id):
        """Get all debt records for a customer."""
        self.cursor.execute('SELECT * FROM debts WHERE customer_id = ?', (customer_id,))
        return self.cursor.fetchall()

    def update_debt(self, debt_id, total_due, paid):
        """Update a debt record."""
        remaining = total_due - paid
        self.cursor.execute('''
        UPDATE debts
        SET total_due = ?, paid = ?, remaining = ?
        WHERE id = ?
        ''', (total_due, paid, remaining, debt_id))
        self.conn.commit()
        return self.cursor.rowcount

    def get_customers_with_debt(self):
        """Get all customers who have outstanding debt."""
        self.cursor.execute('''
        SELECT c.* FROM customers c
        JOIN debts d ON c.id = d.customer_id
        WHERE d.remaining > 0
        GROUP BY c.id
        ''')
        return self.cursor.fetchall()

    # Order statistics and reporting
    def get_daily_orders_total(self):
        """Get the total amount of orders for the current day."""
        today = datetime.now().strftime('%Y-%m-%d')
        self.cursor.execute('''
        SELECT SUM(total_price) as total, SUM(payment) as paid, SUM(remaining) as remaining
        FROM orders
        WHERE date(created_at) = ?
        ''', (today,))
        result = self.cursor.fetchone()
        if result and result[0]:
            return {
                'total': result[0],
                'paid': result[1],
                'remaining': result[2]
            }
        return {'total': 0, 'paid': 0, 'remaining': 0}

    def get_monthly_orders_total(self):
        """Get the total amount of orders for the current month."""
        current_month = datetime.now().strftime('%Y-%m')
        self.cursor.execute('''
        SELECT SUM(total_price) as total, SUM(payment) as paid, SUM(remaining) as remaining
        FROM orders
        WHERE strftime('%Y-%m', created_at) = ?
        ''', (current_month,))
        result = self.cursor.fetchone()
        if result and result[0]:
            return {
                'total': result[0],
                'paid': result[1],
                'remaining': result[2]
            }
        return {'total': 0, 'paid': 0, 'remaining': 0}

    def get_order(self, order_id):
        """Get an order by ID."""
        self.cursor.execute('''
        SELECT o.*, c.name as customer_name, c.phone as customer_phone
        FROM orders o
        JOIN customers c ON o.customer_id = c.id
        WHERE o.id = ?
        ''', (order_id,))
        row = self.cursor.fetchone()
        if row:
            return {
                'id': row[0], 'customer_id': row[1], 'order_type': row[2],
                'total_price': row[3], 'payment': row[4],
                'remaining': row[3] - row[4],  # Calculate remaining
                'created_at': row[5], 'customer_name': row[6], 'customer_phone': row[7]
            }
        return None

    def get_all_orders(self):
        """Get all orders with customer information."""
        self.cursor.execute('''
        SELECT o.*, c.name as customer_name, c.phone as customer_phone
        FROM orders o
        JOIN customers c ON o.customer_id = c.id
        ORDER BY o.created_at DESC
        ''')
        return self.cursor.fetchall()

    def delete_order(self, order_id):
        """Delete an order and related debts."""
        # Get order info first
        order = self.get_order(order_id)
        if order:
            # Delete related debts for this customer if any
            self.cursor.execute('DELETE FROM debts WHERE customer_id = ?', (order['customer_id'],))
            # Delete the order
            self.cursor.execute('DELETE FROM orders WHERE id = ?', (order_id,))
            self.conn.commit()
            return self.cursor.rowcount
        return 0

    def get_daily_orders(self):
        """Get all orders for today."""
        today = datetime.now().strftime('%Y-%m-%d')
        self.cursor.execute('''
        SELECT o.*, c.name as customer_name, c.phone as customer_phone
        FROM orders o
        JOIN customers c ON o.customer_id = c.id
        WHERE date(o.created_at) = ?
        ORDER BY o.created_at DESC
        ''', (today,))
        rows = self.cursor.fetchall()
        return [
            {
                'id': row[0], 'customer_id': row[1], 'order_type': row[2],
                'total_price': row[3], 'payment': row[4],
                'remaining': row[3] - row[4],  # Calculate remaining
                'created_at': row[5], 'customer_name': row[6], 'customer_phone': row[7]
            }
            for row in rows
        ]

    def get_monthly_orders(self):
        """Get all orders for this month."""
        current_month = datetime.now().strftime('%Y-%m')
        self.cursor.execute('''
        SELECT o.*, c.name as customer_name, c.phone as customer_phone
        FROM orders o
        JOIN customers c ON o.customer_id = c.id
        WHERE strftime('%Y-%m', o.created_at) = ?
        ORDER BY o.created_at DESC
        ''', (current_month,))
        rows = self.cursor.fetchall()
        return [
            {
                'id': row[0], 'customer_id': row[1], 'order_type': row[2],
                'total_price': row[3], 'payment': row[4],
                'remaining': row[3] - row[4],  # Calculate remaining
                'created_at': row[5], 'customer_name': row[6], 'customer_phone': row[7]
            }
            for row in rows
        ]

    def get_daily_orders_total(self):
        """Get daily orders total statistics."""
        today = datetime.now().strftime('%Y-%m-%d')
        self.cursor.execute('''
        SELECT
            COALESCE(SUM(total_price), 0) as total,
            COALESCE(SUM(payment), 0) as paid,
            COALESCE(SUM(total_price - payment), 0) as remaining
        FROM orders
        WHERE date(created_at) = ?
        ''', (today,))

        row = self.cursor.fetchone()
        return {
            'total': row[0] if row else 0,
            'paid': row[1] if row else 0,
            'remaining': row[2] if row else 0
        }

    def get_monthly_orders_total(self):
        """Get monthly orders total statistics."""
        current_month = datetime.now().strftime('%Y-%m')
        self.cursor.execute('''
        SELECT
            COALESCE(SUM(total_price), 0) as total,
            COALESCE(SUM(payment), 0) as paid,
            COALESCE(SUM(total_price - payment), 0) as remaining
        FROM orders
        WHERE strftime('%Y-%m', created_at) = ?
        ''', (current_month,))

        row = self.cursor.fetchone()
        return {
            'total': row[0] if row else 0,
            'paid': row[1] if row else 0,
            'remaining': row[2] if row else 0
        }

    def close(self):
        """Close the database connection."""
        self.conn.close()