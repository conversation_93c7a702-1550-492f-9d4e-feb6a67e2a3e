using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using System.Threading.Tasks;

namespace MaarisAlJahra.Helpers
{
    public static class StyleHelper
    {
        public static readonly Color PrimaryColor = Color.FromArgb(42, 63, 84);
        public static readonly Color SecondaryColor = Color.FromArgb(74, 144, 226);
        public static readonly Color AccentColor = Color.FromArgb(52, 152, 219);
        public static readonly Color SuccessColor = Color.FromArgb(46, 204, 113);
        public static readonly Color WarningColor = Color.FromArgb(241, 196, 15);
        public static readonly Color DangerColor = Color.FromArgb(231, 76, 60);
        public static readonly Color LightColor = Color.FromArgb(236, 240, 241);
        public static readonly Color DarkColor = Color.FromArgb(44, 62, 80);
        public static readonly Color GoldColor = Color.FromArgb(241, 196, 15);
        public static readonly Color SilverColor = Color.FromArgb(149, 165, 166);
        public static readonly Color RoseGoldColor = Color.FromArgb(233, 150, 122);
        public static readonly Color EmeraldColor = Color.FromArgb(46, 204, 113);

        public static void ApplyGradientBackground(Control control)
        {
            control.Paint += (sender, e) =>
            {
                using (var brush = new LinearGradientBrush(
                    control.ClientRectangle,
                    PrimaryColor,
                    SecondaryColor,
                    LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, control.ClientRectangle);
                }
            };
        }

        public static void ApplyRoundedCorners(Control control, int radius = 20)
        {
            control.Region = CreateRoundedRegion(control.Size, radius);
            control.Resize += (s, e) =>
            {
                control.Region = CreateRoundedRegion(control.Size, radius);
            };
        }

        private static Region CreateRoundedRegion(Size size, int radius)
        {
            var path = new GraphicsPath();
            path.AddArc(0, 0, radius, radius, 180, 90);
            path.AddArc(size.Width - radius, 0, radius, radius, 270, 90);
            path.AddArc(size.Width - radius, size.Height - radius, radius, radius, 0, 90);
            path.AddArc(0, size.Height - radius, radius, radius, 90, 90);
            path.CloseAllFigures();
            return new Region(path);
        }

        public static void ApplyTextShadow(Control control, string text, Font font, Color textColor, Color shadowColor, Point shadowOffset)
        {
            control.Paint += (sender, e) =>
            {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;
                graphics.TextRenderingHint = System.Drawing.Text.TextRenderingHint.AntiAlias;

                var textSize = graphics.MeasureString(text, font);
                var textLocation = new PointF(
                    (control.Width - textSize.Width) / 2,
                    (control.Height - textSize.Height) / 2
                );

                // Draw shadow
                using (var shadowBrush = new SolidBrush(shadowColor))
                {
                    graphics.DrawString(text, font, shadowBrush,
                        textLocation.X + shadowOffset.X,
                        textLocation.Y + shadowOffset.Y);
                }

                // Draw text
                using (var textBrush = new SolidBrush(textColor))
                {
                    graphics.DrawString(text, font, textBrush, textLocation);
                }
            };
        }

        public static void StyleDataGridView(DataGridView dgv)
        {
            dgv.BackgroundColor = LightColor;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.DefaultCellStyle.SelectionBackColor = AccentColor;
            dgv.DefaultCellStyle.SelectionForeColor = Color.White;
            dgv.DefaultCellStyle.BackColor = Color.White;
            dgv.DefaultCellStyle.ForeColor = DarkColor;
            dgv.DefaultCellStyle.Font = new Font("Segoe UI", 10);
            dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(245, 245, 245);
            dgv.ColumnHeadersDefaultCellStyle.BackColor = PrimaryColor;
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgv.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 11, FontStyle.Bold);
            dgv.ColumnHeadersHeight = 40;
            dgv.RowTemplate.Height = 35;
            dgv.EnableHeadersVisualStyles = false;
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.MultiSelect = false;
        }

        public static Button CreateStyledButton(string text, Color backColor, Size size, EventHandler? clickHandler = null)
        {
            var button = new Button
            {
                Text = text,
                Size = size,
                BackColor = backColor,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            ApplyRoundedCorners(button, 10);

            if (clickHandler != null)
                button.Click += clickHandler;

            // Hover effects
            button.MouseEnter += (s, e) =>
            {
                button.BackColor = ControlPaint.Light(backColor, 0.2f);
            };

            button.MouseLeave += (s, e) =>
            {
                button.BackColor = backColor;
            };

            return button;
        }

        // Enhanced gradient backgrounds with multiple colors
        public static void ApplyMultiColorGradient(Control control, Color[] colors)
        {
            control.Paint += (sender, e) =>
            {
                var rect = control.ClientRectangle;
                using (var brush = new LinearGradientBrush(rect, colors[0], colors[colors.Length - 1], LinearGradientMode.Vertical))
                {
                    if (colors.Length > 2)
                    {
                        var blend = new ColorBlend();
                        blend.Colors = colors;
                        blend.Positions = new float[colors.Length];
                        for (int i = 0; i < colors.Length; i++)
                        {
                            blend.Positions[i] = (float)i / (colors.Length - 1);
                        }
                        brush.InterpolationColors = blend;
                    }
                    e.Graphics.FillRectangle(brush, rect);
                }
            };
        }

        // Smooth button animations
        public static async Task AnimateButtonPress(Button button)
        {
            var originalColor = button.BackColor;
            var pressedColor = ControlPaint.Dark(originalColor, 0.1f);

            button.BackColor = pressedColor;
            await Task.Delay(100);
            button.BackColor = originalColor;
        }

        // Glow effect for important elements
        public static void ApplyGlowEffect(Control control, Color glowColor)
        {
            control.Paint += (sender, e) =>
            {
                var graphics = e.Graphics;
                graphics.SmoothingMode = SmoothingMode.AntiAlias;

                using (var pen = new Pen(glowColor, 3))
                {
                    var rect = new Rectangle(1, 1, control.Width - 2, control.Height - 2);
                    graphics.DrawRectangle(pen, rect);
                }
            };
        }

        // Language-aware text alignment
        public static void SetLanguageAwareAlignment(Control control)
        {
            if (LanguageManager.IsRightToLeft())
            {
                control.RightToLeft = RightToLeft.Yes;
                if (control is Label label)
                    label.TextAlign = ContentAlignment.MiddleRight;
                else if (control is Button button)
                    button.TextAlign = ContentAlignment.MiddleRight;
            }
            else
            {
                control.RightToLeft = RightToLeft.No;
                if (control is Label label)
                    label.TextAlign = ContentAlignment.MiddleLeft;
                else if (control is Button button)
                    button.TextAlign = ContentAlignment.MiddleLeft;
            }
        }
    }
}
