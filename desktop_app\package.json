{"name": "maaris-aljahra-desktop", "version": "2.0.0", "description": "معاريس الجهراء - تطبيق سطح المكتب الحديث", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never"}, "keywords": ["معاريس الجهراء", "customer management", "orders", "desktop app", "electron"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"electron": "^27.3.11", "electron-builder": "^24.6.4"}, "dependencies": {"electron-store": "^8.1.0", "sqlite3": "^5.1.6"}, "build": {"appId": "com.maaris.aljahra", "productName": "معاريس الجهراء", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules/**/*", "node_modules/sqlite3/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "معاريس الجهراء"}}}