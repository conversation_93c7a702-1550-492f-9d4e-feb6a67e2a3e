const { app, BrowserWindow, Menu, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

// Keep a global reference of the window object
let mainWindow;
let backendProcess;

// Enable live reload for development
if (process.env.NODE_ENV === 'development') {
  require('electron-reload')(__dirname, {
    electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
    hardResetMethod: 'exit'
  });
}

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    icon: path.join(__dirname, 'assets', 'icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: true, // Enable web security
      allowRunningInsecureContent: false,
      experimentalFeatures: false
    },
    titleBarStyle: 'default',
    show: false, // Don't show until ready
    backgroundColor: '#f8f9fa',
    autoHideMenuBar: false // Show menu bar
  });

  // Set window title
  mainWindow.setTitle('معاريس الجهراء - نظام إدارة العملاء والطلبات');

  // Load the app
  mainWindow.loadFile('renderer/index.html');

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Focus on window
    if (process.platform === 'darwin') {
      app.dock.show();
    }
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Development tools
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }
}

function startBackendServer() {
  return new Promise((resolve, reject) => {
    const pythonPath = process.platform === 'win32' ? 'py' : 'python3';
    const serverPath = path.join(__dirname, 'backend', 'server.py');

    // Check if Python is available
    const pythonCheck = spawn(pythonPath, ['--version']);

    pythonCheck.on('error', (error) => {
      console.error('Python not found:', error);
      reject(new Error('Python not found. Please install Python.'));
    });

    pythonCheck.on('close', (code) => {
      if (code === 0) {
        // Start the backend server with UTF-8 encoding
        backendProcess = spawn(pythonPath, [serverPath], {
          cwd: __dirname,
          stdio: ['pipe', 'pipe', 'pipe'],
          env: {
            ...process.env,
            PYTHONIOENCODING: 'utf-8',
            PYTHONUNBUFFERED: '1'
          }
        });

        let serverStarted = false;

        backendProcess.stdout.on('data', (data) => {
          const output = data.toString();
          console.log(`Backend: ${output}`);
          if (output.includes('Server is ready') && !serverStarted) {
            serverStarted = true;
            resolve();
          }
        });

        backendProcess.stderr.on('data', (data) => {
          const error = data.toString();
          console.error(`Backend Error: ${error}`);
          // Don't reject on stderr, as some warnings are normal
        });

        backendProcess.on('close', (code) => {
          console.log(`Backend process exited with code ${code}`);
          if (code !== 0 && !serverStarted) {
            reject(new Error(`Backend server failed to start (exit code: ${code})`));
          }
        });

        backendProcess.on('error', (error) => {
          console.error('Backend process error:', error);
          if (!serverStarted) {
            reject(error);
          }
        });

        // Resolve after a delay if no explicit ready message
        setTimeout(() => {
          if (!serverStarted) {
            console.log('Backend server timeout - assuming started');
            resolve();
          }
        }, 5000);
      } else {
        reject(new Error('Python version check failed'));
      }
    });
  });
}

function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          submenu: [
            {
              label: 'عميل جديد',
              accelerator: 'CmdOrCtrl+N',
              click: () => {
                mainWindow.webContents.send('menu-action', 'new-customer');
              }
            },
            {
              label: 'طلب جديد',
              accelerator: 'CmdOrCtrl+Shift+N',
              click: () => {
                mainWindow.webContents.send('menu-action', 'new-order');
              }
            }
          ]
        },
        { type: 'separator' },
        {
          label: 'إعدادات',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            mainWindow.webContents.send('menu-action', 'settings');
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        {
          label: 'الرئيسية',
          accelerator: 'CmdOrCtrl+1',
          click: () => {
            mainWindow.webContents.send('menu-action', 'show-dashboard');
          }
        },
        {
          label: 'العملاء',
          accelerator: 'CmdOrCtrl+2',
          click: () => {
            mainWindow.webContents.send('menu-action', 'show-customers');
          }
        },
        {
          label: 'الطلبات',
          accelerator: 'CmdOrCtrl+3',
          click: () => {
            mainWindow.webContents.send('menu-action', 'show-orders');
          }
        },
        {
          label: 'الديون',
          accelerator: 'CmdOrCtrl+4',
          click: () => {
            mainWindow.webContents.send('menu-action', 'show-debts');
          }
        },
        {
          label: 'التقارير',
          accelerator: 'CmdOrCtrl+5',
          click: () => {
            mainWindow.webContents.send('menu-action', 'show-reports');
          }
        },
        { type: 'separator' },
        {
          label: 'تحديث',
          accelerator: 'CmdOrCtrl+R',
          click: () => {
            mainWindow.reload();
          }
        },
        {
          label: 'ملء الشاشة',
          accelerator: process.platform === 'darwin' ? 'Ctrl+Cmd+F' : 'F11',
          click: () => {
            mainWindow.setFullScreen(!mainWindow.isFullScreen());
          }
        }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول التطبيق',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'حول التطبيق',
              message: 'معاريس الجهراء',
              detail: 'نظام إدارة العملاء والطلبات\nالإصدار 2.0.0\n\nتطبيق حديث ومتطور لإدارة أعمالك',
              buttons: ['موافق']
            });
          }
        }
      ]
    }
  ];

  // macOS specific menu adjustments
  if (process.platform === 'darwin') {
    template.unshift({
      label: app.getName(),
      submenu: [
        {
          label: 'حول ' + app.getName(),
          role: 'about'
        },
        { type: 'separator' },
        {
          label: 'إخفاء ' + app.getName(),
          accelerator: 'Command+H',
          role: 'hide'
        },
        {
          label: 'إخفاء الآخرين',
          accelerator: 'Command+Shift+H',
          role: 'hideothers'
        },
        {
          label: 'إظهار الكل',
          role: 'unhide'
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: 'Command+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    });
  }

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Disable hardware acceleration to prevent GPU warnings
app.disableHardwareAcceleration();

// Set additional command line switches for stability
app.commandLine.appendSwitch('--disable-gpu-sandbox');
app.commandLine.appendSwitch('--disable-software-rasterizer');
app.commandLine.appendSwitch('--disable-background-timer-throttling');
app.commandLine.appendSwitch('--disable-backgrounding-occluded-windows');
app.commandLine.appendSwitch('--disable-renderer-backgrounding');

// App event handlers
app.whenReady().then(async () => {
  try {
    // Start backend server
    console.log('Starting backend server...');
    await startBackendServer();
    console.log('Backend server started successfully');

    // Create window
    createWindow();

    // Create menu
    createMenu();

  } catch (error) {
    console.error('Failed to start application:', error);
    dialog.showErrorBox('خطأ في بدء التطبيق', 'فشل في تشغيل الخادم الداخلي. تأكد من تثبيت Python.');
    app.quit();
  }
});

app.on('window-all-closed', () => {
  // Kill backend process
  if (backendProcess) {
    backendProcess.kill();
  }

  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On macOS, re-create window when dock icon is clicked
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.on('before-quit', () => {
  // Kill backend process before quitting
  if (backendProcess) {
    backendProcess.kill();
  }
});

// IPC handlers
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-save-dialog', async () => {
  const result = await dialog.showSaveDialog(mainWindow, {
    filters: [
      { name: 'JSON Files', extensions: ['json'] },
      { name: 'All Files', extensions: ['*'] }
    ]
  });
  return result;
});

ipcMain.handle('show-open-dialog', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    filters: [
      { name: 'JSON Files', extensions: ['json'] },
      { name: 'All Files', extensions: ['*'] }
    ],
    properties: ['openFile']
  });
  return result;
});
