/* <PERSON>'<PERSON><PERSON> Al <PERSON> - Application Stylesheet */

/* Global styles */
QWidget {
    font-family: Arial, sans-serif;
    font-size: 10pt;
}

QMainWindow {
    background-color: #F5F5F5;
}

/* Header styles */
#headerFrame {
    background-color: #B3E0FF;
    border-radius: 10px;
    padding: 10px;
}

/* Button styles */
QPushButton {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: bold;
}

QPushButton:hover {
    opacity: 0.9;
}

QPushButton:pressed {
    opacity: 0.7;
}

/* Form styles */
QLineEdit, QComboBox {
    padding: 6px;
    border: 1px solid #C4C4C4;
    border-radius: 4px;
    background-color: white;
}

QLineEdit:focus, QComboBox:focus {
    border: 1px solid #2196F3;
}

/* List styles */
QListWidget {
    border: 1px solid #C4C4C4;
    border-radius: 4px;
    background-color: white;
    padding: 4px;
}

QListWidget::item {
    padding: 6px;
    border-bottom: 1px solid #E0E0E0;
}

QListWidget::item:selected {
    background-color: #2196F3;
    color: white;
}

QListWidget::item:hover {
    background-color: #E3F2FD;
}

/* Tab styles */
QTabWidget::pane {
    border: 1px solid #C4C4C4;
    border-radius: 5px;
    padding: 5px;
}

QTabBar::tab {
    background-color: #E0E0E0;
    border: 1px solid #C4C4C4;
    border-bottom-color: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 8px 12px;
    margin-right: 2px;
}

QTabBar::tab:selected {
    background-color: #FFFFFF;
    border-bottom-color: #FFFFFF;
}

QTabBar::tab:hover {
    background-color: #F0F0F0;
}

/* Frame styles */
QFrame {
    border-radius: 4px;
}

/* Label styles */
QLabel {
    color: #333333;
}

/* Specific button styles */
QPushButton#addButton {
    background-color: #4CAF50;
    color: white;
}

QPushButton#editButton {
    background-color: #2196F3;
    color: white;
}

QPushButton#deleteButton {
    background-color: #F44336;
    color: white;
}

QPushButton#saveButton {
    background-color: #9C27B0;
    color: white;
}

/* Splash screen styles */
#splashScreen {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
}

/* Role selection styles */
#roleButton {
    min-width: 200px;
    min-height: 150px;
    font-size: 16pt;
    border-radius: 15px;
}

#managerButton {
    background-color: #8A2BE2;
    color: white;
}

#employeeButton {
    background-color: #4682B4;
    color: white;
}
