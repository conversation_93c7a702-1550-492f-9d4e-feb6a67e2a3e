#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - شاشة البداية
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QGraphicsOpacityEffect,
    QSizePolicy
)
from PySide6.QtCore import (
    Qt, QPropertyAnimation, QEasingCurve,
    QSize, QTimer, Property
)
from PySide6.QtGui import QFont, QColor, QPalette, QLinearGradient

class SplashScreen(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("معاريس الجهراء")
        self.setFixedSize(600, 400)
        self.setWindowFlag(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # Center the window
        self.center_on_screen()

        # Setup UI
        self.setup_ui()

        # Start animations
        QTimer.singleShot(100, self.start_animations)

    def center_on_screen(self):
        """Center the window on the screen."""
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import QRect

        screen_geometry = QApplication.primaryScreen().geometry()
        x = (screen_geometry.width() - self.width()) // 2
        y = (screen_geometry.height() - self.height()) // 2

        self.move(x, y)

    def setup_ui(self):
        """Setup the UI components."""
        # Main layout
        layout = QVBoxLayout()
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setAlignment(Qt.AlignCenter)

        # Create logo label
        self.logo_label = QLabel("معاريس الجهراء")
        self.logo_label.setAlignment(Qt.AlignCenter)

        # Set font for logo
        logo_font = QFont("Traditional Arabic", 42, QFont.Bold)
        self.logo_label.setFont(logo_font)

        # Set colors (blue and gold)
        palette = self.logo_label.palette()
        gradient = QLinearGradient(0, 0, self.logo_label.width(), 0)
        gradient.setColorAt(0, QColor(0, 102, 204))  # Blue
        gradient.setColorAt(1, QColor(255, 215, 0))  # Gold

        palette.setColor(QPalette.WindowText, QColor(0, 102, 204))  # Blue
        self.logo_label.setPalette(palette)

        # Set size policy
        self.logo_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Add to layout
        layout.addWidget(self.logo_label)

        # Set layout
        self.setLayout(layout)

        # Create opacity effect for fade-in
        self.opacity_effect = QGraphicsOpacityEffect(self.logo_label)
        self.opacity_effect.setOpacity(0)
        self.logo_label.setGraphicsEffect(self.opacity_effect)

        # Create scale property for zoom effect
        self._scale = 0.5

    def _get_scale(self):
        return self._scale

    def _set_scale(self, scale):
        self._scale = scale
        # Apply scale to font
        font = self.logo_label.font()
        font.setPointSizeF(42 * scale)
        self.logo_label.setFont(font)

    scale = Property(float, _get_scale, _set_scale)

    def start_animations(self):
        """Start the animations for the splash screen."""
        # Fade-in animation
        self.fade_animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        self.fade_animation.setDuration(1000)
        self.fade_animation.setStartValue(0)
        self.fade_animation.setEndValue(1)
        self.fade_animation.setEasingCurve(QEasingCurve.InOutQuad)

        # Zoom animation
        self.zoom_animation = QPropertyAnimation(self, b"scale")
        self.zoom_animation.setDuration(1000)
        self.zoom_animation.setStartValue(0.5)
        self.zoom_animation.setEndValue(1.0)
        self.zoom_animation.setEasingCurve(QEasingCurve.OutBack)

        # Start animations
        self.fade_animation.start()
        self.zoom_animation.start()
