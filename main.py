#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - تطبيق إدارة
"""

import sys

try:
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import QTimer, QFile, QTextStream, Qt, QLocale
    from PySide6.QtGui import QFont
    PYSIDE6_AVAILABLE = True
except ImportError:
    print("PySide6 is not installed. Please install it with: pip install PySide6")
    PYSIDE6_AVAILABLE = False

from ui.splash_screen import SplashScreen
from ui.role_selection import RoleSelectionScreen
from ui.manager_interface import ManagerInterface
from ui.employee_interface import EmployeeInterface

class MainApplication:
    def __init__(self, skip_splash=False):
        # Check if QApplication already exists
        if not QApplication.instance():
            self.app = QApplication(sys.argv)

            # Set application to use Arabic locale
            arabic_locale = QLocale(QLocale.Arabic, QLocale.Egypt)
            QLocale.setDefault(arabic_locale)

            # Set application to use right-to-left layout
            self.app.setLayoutDirection(Qt.RightToLeft)

            # Load and set Arabic font
            self.load_arabic_fonts()

            # Load application stylesheet
            self.load_stylesheet()
        else:
            self.app = QApplication.instance()

        self.splash_screen = None
        self.role_selection = None
        self.manager_interface = None
        self.employee_interface = None

        # If skip_splash is True, go directly to role selection
        if skip_splash:
            self.show_role_selection()
        else:
            # Start with splash screen
            self.splash_screen = SplashScreen()
            self.splash_screen.show()

            # Connect to role selection screen after 3 seconds
            QTimer.singleShot(3000, self.show_role_selection)

    def start(self):
        """Start the application main loop."""
        # Execute application
        return self.app.exec()

    def show_role_selection(self):
        """Show the role selection screen."""
        # Hide splash screen and show role selection
        if self.splash_screen:
            self.splash_screen.hide()

        self.role_selection = RoleSelectionScreen()
        self.role_selection.manager_selected.connect(self.show_manager_interface)
        self.role_selection.employee_selected.connect(self.show_employee_interface)
        self.role_selection.show()

    def show_manager_interface(self):
        # Hide role selection and show manager interface
        if self.role_selection:
            self.role_selection.hide()

        self.manager_interface = ManagerInterface()
        self.manager_interface.show()

    def show_employee_interface(self):
        # Hide role selection and show employee interface
        if self.role_selection:
            self.role_selection.hide()

        self.employee_interface = EmployeeInterface()
        self.employee_interface.show()

    def load_arabic_fonts(self):
        """Load Arabic fonts for the application."""
        # Try to load system Arabic fonts
        arabic_fonts = ["Arial", "Tahoma", "Segoe UI", "Dubai", "Traditional Arabic"]
        default_font = None

        for font_name in arabic_fonts:
            font = QFont(font_name, 10)
            if font.exactMatch():
                default_font = font
                break

        if default_font:
            self.app.setFont(default_font)
        else:
            # If no Arabic font found, use default
            default_font = QFont("Arial", 10)
            self.app.setFont(default_font)

    def load_stylesheet(self):
        """Load application stylesheet from resources."""
        style_file = QFile("resources/style.qss")
        if style_file.open(QFile.ReadOnly | QFile.Text):
            stream = QTextStream(style_file)
            stylesheet = stream.readAll()
            self.app.setStyleSheet(stylesheet)
            style_file.close()

if __name__ == "__main__":
    app = MainApplication()
    sys.exit(app.start())
