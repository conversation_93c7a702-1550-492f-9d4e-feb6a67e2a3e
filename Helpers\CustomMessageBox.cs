using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace MaarisAlJahra.Helpers
{
    public static class CustomMessageBox
    {
        public static DialogResult Show(string message, string title = "رسالة", 
            MessageBoxButtons buttons = MessageBoxButtons.OK, 
            MessageBoxIcon icon = MessageBoxIcon.Information)
        {
            var form = new Form();
            var result = DialogResult.OK;

            // Form setup
            form.Text = title;
            form.Size = new Size(400, 200);
            form.StartPosition = FormStartPosition.CenterParent;
            form.FormBorderStyle = FormBorderStyle.None;
            form.BackColor = Color.White;
            form.Font = new Font("Segoe UI", 12);

            StyleHelper.ApplyRoundedCorners(form, 15);

            // Header panel
            var headerPanel = new Panel
            {
                Size = new Size(form.Width, 50),
                Location = new Point(0, 0),
                BackColor = StyleHelper.PrimaryColor
            };

            var titleLabel = new Label
            {
                Text = title,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                AutoSize = true,
                Location = new Point(15, 15)
            };

            headerPanel.Controls.Add(titleLabel);

            // Message label
            var messageLabel = new Label
            {
                Text = message,
                Font = new Font("Segoe UI", 11),
                ForeColor = StyleHelper.DarkColor,
                AutoSize = false,
                Size = new Size(350, 80),
                Location = new Point(25, 70),
                TextAlign = ContentAlignment.MiddleLeft
            };

            // Button panel
            var buttonPanel = new Panel
            {
                Size = new Size(form.Width, 50),
                Location = new Point(0, form.Height - 50),
                BackColor = Color.Transparent
            };

            // Create buttons based on type
            CreateButtons(buttonPanel, buttons, ref result, form);

            form.Controls.Add(headerPanel);
            form.Controls.Add(messageLabel);
            form.Controls.Add(buttonPanel);

            // Add icon
            if (icon != MessageBoxIcon.None)
            {
                var iconLabel = new Label
                {
                    Size = new Size(32, 32),
                    Location = new Point(form.Width - 50, 70),
                    BackColor = Color.Transparent
                };

                iconLabel.Paint += (s, e) =>
                {
                    var iconColor = GetIconColor(icon);
                    using (var brush = new SolidBrush(iconColor))
                    {
                        e.Graphics.FillEllipse(brush, 0, 0, 32, 32);
                        
                        var iconText = GetIconText(icon);
                        using (var textBrush = new SolidBrush(Color.White))
                        using (var font = new Font("Segoe UI", 16, FontStyle.Bold))
                        {
                            var textSize = e.Graphics.MeasureString(iconText, font);
                            var x = (32 - textSize.Width) / 2;
                            var y = (32 - textSize.Height) / 2;
                            e.Graphics.DrawString(iconText, font, textBrush, x, y);
                        }
                    }
                };

                form.Controls.Add(iconLabel);
            }

            // Show form
            form.ShowDialog();
            return result;
        }

        private static void CreateButtons(Panel buttonPanel, MessageBoxButtons buttons, 
            ref DialogResult result, Form form)
        {
            var buttonWidth = 80;
            var buttonHeight = 35;
            var spacing = 10;

            switch (buttons)
            {
                case MessageBoxButtons.OK:
                    var okButton = CreateButton("موافق", StyleHelper.AccentColor, buttonWidth, buttonHeight);
                    okButton.Location = new Point((buttonPanel.Width - buttonWidth) / 2, 8);
                    okButton.Click += (s, e) => { result = DialogResult.OK; form.Close(); };
                    buttonPanel.Controls.Add(okButton);
                    break;

                case MessageBoxButtons.YesNo:
                    var yesButton = CreateButton("نعم", StyleHelper.SuccessColor, buttonWidth, buttonHeight);
                    var noButton = CreateButton("لا", StyleHelper.DangerColor, buttonWidth, buttonHeight);
                    
                    var totalWidth = buttonWidth * 2 + spacing;
                    var startX = (buttonPanel.Width - totalWidth) / 2;
                    
                    yesButton.Location = new Point(startX, 8);
                    noButton.Location = new Point(startX + buttonWidth + spacing, 8);
                    
                    yesButton.Click += (s, e) => { result = DialogResult.Yes; form.Close(); };
                    noButton.Click += (s, e) => { result = DialogResult.No; form.Close(); };
                    
                    buttonPanel.Controls.Add(yesButton);
                    buttonPanel.Controls.Add(noButton);
                    break;

                case MessageBoxButtons.YesNoCancel:
                    var yesBtn = CreateButton("نعم", StyleHelper.SuccessColor, buttonWidth, buttonHeight);
                    var noBtn = CreateButton("لا", StyleHelper.DangerColor, buttonWidth, buttonHeight);
                    var cancelBtn = CreateButton("إلغاء", StyleHelper.LightColor, buttonWidth, buttonHeight);
                    
                    var totalWidthYNC = buttonWidth * 3 + spacing * 2;
                    var startXYNC = (buttonPanel.Width - totalWidthYNC) / 2;
                    
                    yesBtn.Location = new Point(startXYNC, 8);
                    noBtn.Location = new Point(startXYNC + buttonWidth + spacing, 8);
                    cancelBtn.Location = new Point(startXYNC + (buttonWidth + spacing) * 2, 8);
                    
                    yesBtn.Click += (s, e) => { result = DialogResult.Yes; form.Close(); };
                    noBtn.Click += (s, e) => { result = DialogResult.No; form.Close(); };
                    cancelBtn.Click += (s, e) => { result = DialogResult.Cancel; form.Close(); };
                    
                    buttonPanel.Controls.Add(yesBtn);
                    buttonPanel.Controls.Add(noBtn);
                    buttonPanel.Controls.Add(cancelBtn);
                    break;
            }
        }

        private static Button CreateButton(string text, Color backColor, int width, int height)
        {
            var button = new Button
            {
                Text = text,
                Size = new Size(width, height),
                BackColor = backColor,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            StyleHelper.ApplyRoundedCorners(button, 8);

            return button;
        }

        private static Color GetIconColor(MessageBoxIcon icon)
        {
            return icon switch
            {
                MessageBoxIcon.Information => StyleHelper.AccentColor,
                MessageBoxIcon.Warning => StyleHelper.WarningColor,
                MessageBoxIcon.Error => StyleHelper.DangerColor,
                MessageBoxIcon.Question => StyleHelper.AccentColor,
                _ => StyleHelper.AccentColor
            };
        }

        private static string GetIconText(MessageBoxIcon icon)
        {
            return icon switch
            {
                MessageBoxIcon.Information => "i",
                MessageBoxIcon.Warning => "!",
                MessageBoxIcon.Error => "×",
                MessageBoxIcon.Question => "?",
                _ => "i"
            };
        }
    }
}
