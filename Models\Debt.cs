using System.ComponentModel.DataAnnotations;

namespace MaarisAlJahra.Models
{
    public class Debt
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string CustomerName { get; set; } = string.Empty;
        
        [StringLength(20)]
        public string CustomerPhone { get; set; } = string.Empty;
        
        public decimal Amount { get; set; }
        
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? PaidAt { get; set; }
        
        public bool IsPaid { get; set; } = false;
        
        public int? OrderId { get; set; }
    }
}
