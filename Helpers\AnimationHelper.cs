using System;
using System.Drawing;
using System.Windows.Forms;

namespace MaarisAlJahra.Helpers
{
    public static class AnimationHelper
    {
        public static async Task FadeIn(Form form, int duration = 500)
        {
            form.Opacity = 0;
            form.Show();

            var timer = new System.Windows.Forms.Timer();
            var startTime = DateTime.Now;

            timer.Interval = 10;
            timer.Tick += (s, e) =>
            {
                var elapsed = DateTime.Now - startTime;
                var progress = Math.Min(1.0, elapsed.TotalMilliseconds / duration);

                form.Opacity = progress;

                if (progress >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };

            timer.Start();
            await Task.Delay(duration);
        }

        public static async Task FadeOut(Form form, int duration = 500)
        {
            var timer = new System.Windows.Forms.Timer();
            var startTime = DateTime.Now;
            var startOpacity = form.Opacity;

            timer.Interval = 10;
            timer.Tick += (s, e) =>
            {
                var elapsed = DateTime.Now - startTime;
                var progress = Math.Min(1.0, elapsed.TotalMilliseconds / duration);

                form.Opacity = startOpacity * (1.0 - progress);

                if (progress >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                    form.Hide();
                }
            };

            timer.Start();
            await Task.Delay(duration);
        }

        public static async Task ScaleButton(Control button, float scale = 1.1f, int duration = 200)
        {
            var originalSize = button.Size;
            var targetSize = new Size((int)(originalSize.Width * scale), (int)(originalSize.Height * scale));
            var originalLocation = button.Location;
            var targetLocation = new Point(
                originalLocation.X - (targetSize.Width - originalSize.Width) / 2,
                originalLocation.Y - (targetSize.Height - originalSize.Height) / 2
            );

            await AnimateControl(button, originalSize, targetSize, originalLocation, targetLocation, duration);
            await Task.Delay(100);
            await AnimateControl(button, targetSize, originalSize, targetLocation, originalLocation, duration);
        }

        private static async Task AnimateControl(Control control, Size fromSize, Size toSize, Point fromLocation, Point toLocation, int duration)
        {
            var timer = new System.Windows.Forms.Timer();
            var startTime = DateTime.Now;

            timer.Interval = 10;
            timer.Tick += (s, e) =>
            {
                var elapsed = DateTime.Now - startTime;
                var progress = Math.Min(1.0, elapsed.TotalMilliseconds / duration);

                var currentWidth = (int)(fromSize.Width + (toSize.Width - fromSize.Width) * progress);
                var currentHeight = (int)(fromSize.Height + (toSize.Height - fromSize.Height) * progress);
                var currentX = (int)(fromLocation.X + (toLocation.X - fromLocation.X) * progress);
                var currentY = (int)(fromLocation.Y + (toLocation.Y - fromLocation.Y) * progress);

                control.Size = new Size(currentWidth, currentHeight);
                control.Location = new Point(currentX, currentY);

                if (progress >= 1.0)
                {
                    timer.Stop();
                    timer.Dispose();
                }
            };

            timer.Start();
            await Task.Delay(duration);
        }

        public static async Task ShakeControl(Control control, int intensity = 10, int duration = 500)
        {
            var originalLocation = control.Location;
            var random = new Random();
            var timer = new System.Windows.Forms.Timer();
            var startTime = DateTime.Now;

            timer.Interval = 50;
            timer.Tick += (s, e) =>
            {
                var elapsed = DateTime.Now - startTime;

                if (elapsed.TotalMilliseconds >= duration)
                {
                    control.Location = originalLocation;
                    timer.Stop();
                    timer.Dispose();
                    return;
                }

                var offsetX = random.Next(-intensity, intensity + 1);
                var offsetY = random.Next(-intensity, intensity + 1);
                control.Location = new Point(originalLocation.X + offsetX, originalLocation.Y + offsetY);
            };

            timer.Start();
            await Task.Delay(duration);
        }
    }
}
