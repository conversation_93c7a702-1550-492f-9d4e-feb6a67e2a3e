using System;
using System.Threading.Tasks;
using MaarisAlJahra;
using MaarisAlJahra.Models;

class TestDatabase
{
    static async Task Main(string[] args)
    {
        Console.WriteLine("Testing database connection...");
        
        try
        {
            using var context = new DatabaseContext();
            
            // Ensure database is created
            await context.Database.EnsureCreatedAsync();
            Console.WriteLine("✓ Database created successfully");
            
            // Test adding a worker
            var worker = new Worker
            {
                Name = "أحمد محمد",
                Phone = "12345678",
                Position = "مطور",
                Salary = 5000,
                HireDate = DateTime.Now,
                IsActive = true
            };
            
            context.Workers.Add(worker);
            await context.SaveChangesAsync();
            Console.WriteLine("✓ Worker added successfully");
            
            // Test adding an order
            var order = new Order
            {
                CustomerName = "عميل تجريبي",
                CustomerPhone = "87654321",
                OrderType = "طلب تجريبي",
                TotalPrice = 1000,
                Payment = 500,
                CreatedAt = DateTime.Now,
                IsCompleted = false
            };
            
            context.Orders.Add(order);
            await context.SaveChangesAsync();
            Console.WriteLine("✓ Order added successfully");
            
            // Test adding a debt (automatically created from order)
            if (order.Remaining > 0)
            {
                var debt = new Debt
                {
                    CustomerName = order.CustomerName,
                    CustomerPhone = order.CustomerPhone,
                    Amount = order.Remaining,
                    Description = $"دين من الطلب: {order.OrderType}",
                    CreatedAt = DateTime.Now,
                    OrderId = order.Id,
                    IsPaid = false
                };
                
                context.Debts.Add(debt);
                await context.SaveChangesAsync();
                Console.WriteLine("✓ Debt added successfully");
            }
            
            Console.WriteLine("\n=== Database Test Results ===");
            Console.WriteLine($"Workers count: {context.Workers.Count()}");
            Console.WriteLine($"Orders count: {context.Orders.Count()}");
            Console.WriteLine($"Debts count: {context.Debts.Count()}");
            
            Console.WriteLine("\n✓ All database operations completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }
}
