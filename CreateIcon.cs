using System;
using MaarisAlJahra.Helpers;

namespace MaarisAlJahra
{
    class CreateIcon
    {
        static void Main(string[] args)
        {
            try
            {
                Console.WriteLine("Creating application icon...");
                IconGenerator.CreateIconFile("MaarisAlJahra.ico");
                Console.WriteLine("Icon created successfully: MaarisAlJahra.ico");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating icon: {ex.Message}");
            }
        }
    }
}
