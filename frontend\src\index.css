@tailwind base;
@tailwind components;
@tailwind utilities;

/* Arabic font imports */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');

/* Global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Noto Sans Arabic', 'Traditional Arabic', Arial, sans-serif;
  direction: rtl;
  text-align: right;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #f39c12;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #e67e22;
}

/* Custom components */
@layer components {
  .btn-primary {
    @apply bg-primary-400 hover:bg-primary-500 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply bg-secondary-500 hover:bg-secondary-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }
  
  .btn-danger {
    @apply bg-red-500 hover:bg-red-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }
  
  .input-field {
    @apply w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:border-primary-400 focus:outline-none transition-colors duration-200 text-right;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 p-6;
  }
  
  .gradient-header {
    @apply bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600 text-white;
  }
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.6s ease-out;
}

/* Responsive text sizes for Arabic */
.text-arabic-sm {
  font-size: 0.875rem;
  line-height: 1.5;
}

.text-arabic-base {
  font-size: 1rem;
  line-height: 1.6;
}

.text-arabic-lg {
  font-size: 1.125rem;
  line-height: 1.7;
}

.text-arabic-xl {
  font-size: 1.25rem;
  line-height: 1.8;
}

.text-arabic-2xl {
  font-size: 1.5rem;
  line-height: 1.9;
}

.text-arabic-3xl {
  font-size: 1.875rem;
  line-height: 2;
}

/* Loading spinner */
.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #f39c12;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Hover effects */
.hover-lift {
  transition: transform 0.2s ease-in-out;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}
