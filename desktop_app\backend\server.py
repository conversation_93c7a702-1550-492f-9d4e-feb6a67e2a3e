#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - Desktop Backend Server
Backend server for the desktop application
"""

import http.server
import socketserver
import json
import sqlite3
import os
from datetime import datetime
from urllib.parse import urlparse

# Database path - look for database in parent directory or create new one
DB_PATHS = [
    os.path.join(os.path.dirname(__file__), '..', '..', 'maaris_aljahra.db'),
    os.path.join(os.path.dirname(__file__), '..', 'maaris_aljahra.db'),
    os.path.join(os.path.dirname(__file__), 'maaris_aljahra.db')
]

DB_PATH = None
for path in DB_PATHS:
    if os.path.exists(path):
        DB_PATH = path
        break

if not DB_PATH:
    # Create new database in the backend directory
    DB_PATH = os.path.join(os.path.dirname(__file__), 'maaris_aljahra.db')
    print(f"Creating new database at: {DB_PATH}")

class DesktopAPIHandler(http.server.BaseHTTPRequestHandler):
    def log_message(self, format, *args):
        # Suppress default logging to reduce console noise
        # Unused parameters are intentional for override
        pass

    def do_GET(self):
        """Handle GET requests - Desktop Only."""
        # Security check: Only allow localhost connections
        client_ip = self.client_address[0]
        if client_ip not in ['127.0.0.1', '::1']:
            self.send_error(403, "Access denied - Desktop application only")
            return

        parsed_path = urlparse(self.path)
        path = parsed_path.path

        # Add CORS headers (restricted to localhost only)
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', 'http://localhost:3000')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        try:
            if path == '/':
                response = {"message": "Ma'aris Al Jahra Desktop API is running!", "version": "2.0.0"}
            elif path == '/api/customers':
                response = self.get_customers()
            elif path == '/api/orders':
                response = self.get_orders()
            elif path == '/api/debts':
                response = self.get_debts()
            elif path == '/api/statistics/daily':
                response = self.get_daily_statistics()
            elif path == '/api/statistics/monthly':
                response = self.get_monthly_statistics()
            elif path.startswith('/api/customers/search/'):
                query = path.split('/')[-1]
                response = self.search_customers(query)
            elif path.startswith('/api/customers/') and path != '/api/customers':
                # Get single customer by ID
                try:
                    customer_id = int(path.split('/')[-1])
                    response = self.get_customer_by_id(customer_id)
                    if not response:
                        response = {"error": "Customer not found"}
                except ValueError:
                    response = {"error": "Invalid customer ID"}
            else:
                response = {"error": "Not found"}

            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        except Exception as e:
            error_response = {"error": str(e)}
            self.wfile.write(json.dumps(error_response).encode('utf-8'))

    def do_POST(self):
        """Handle POST requests - Desktop Only."""
        # Security check: Only allow localhost connections
        client_ip = self.client_address[0]
        if client_ip not in ['127.0.0.1', '::1']:
            self.send_error(403, "Access denied - Desktop application only")
            return

        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)

        # Add CORS headers (restricted to localhost only)
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', 'http://localhost:3000')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        try:
            data = json.loads(post_data.decode('utf-8'))
            parsed_path = urlparse(self.path)
            path = parsed_path.path

            if path == '/api/customers':
                response = self.create_customer(data)
            elif path == '/api/orders':
                response = self.create_order(data)
            else:
                response = {"error": "Not found"}

            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        except Exception as e:
            error_response = {"error": str(e)}
            self.wfile.write(json.dumps(error_response).encode('utf-8'))

    def do_PUT(self):
        """Handle PUT requests - Desktop Only."""
        # Security check: Only allow localhost connections
        client_ip = self.client_address[0]
        if client_ip not in ['127.0.0.1', '::1']:
            self.send_error(403, "Access denied - Desktop application only")
            return

        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)

        # Add CORS headers (restricted to localhost only)
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', 'http://localhost:3000')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        try:
            data = json.loads(post_data.decode('utf-8'))
            parsed_path = urlparse(self.path)
            path = parsed_path.path

            if path.startswith('/api/customers/') and path != '/api/customers':
                # Update customer by ID
                try:
                    customer_id = int(path.split('/')[-1])
                    response = self.update_customer(customer_id, data)
                    if not response:
                        response = {"error": "Customer not found"}
                except ValueError:
                    response = {"error": "Invalid customer ID"}
            else:
                response = {"error": "Not found"}

            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        except Exception as e:
            error_response = {"error": str(e)}
            self.wfile.write(json.dumps(error_response).encode('utf-8'))

    def do_DELETE(self):
        """Handle DELETE requests - Desktop Only."""
        # Security check: Only allow localhost connections
        client_ip = self.client_address[0]
        if client_ip not in ['127.0.0.1', '::1']:
            self.send_error(403, "Access denied - Desktop application only")
            return

        parsed_path = urlparse(self.path)
        path = parsed_path.path

        # Add CORS headers (restricted to localhost only)
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', 'http://localhost:3000')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        try:
            if path.startswith('/api/customers/') and path != '/api/customers':
                # Delete customer by ID
                try:
                    customer_id = int(path.split('/')[-1])
                    response = self.delete_customer(customer_id)
                    if not response:
                        response = {"error": "Customer not found"}
                except ValueError:
                    response = {"error": "Invalid customer ID"}
            else:
                response = {"error": "Not found"}

            self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
        except Exception as e:
            error_response = {"error": str(e)}
            self.wfile.write(json.dumps(error_response).encode('utf-8'))

    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS - Desktop Only."""
        # Security check: Only allow localhost connections
        client_ip = self.client_address[0]
        if client_ip not in ['127.0.0.1', '::1']:
            self.send_error(403, "Access denied - Desktop application only")
            return

        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', 'http://localhost:3000')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

    def get_db_connection(self):
        """Get database connection."""
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row

        # Create tables if they don't exist
        self.create_tables(conn)

        return conn

    def create_tables(self, conn):
        """Create database tables if they don't exist."""
        try:
            # Check if tables exist
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='customers'")
            if cursor.fetchone():
                # Check if debts table has the new schema
                cursor = conn.execute("PRAGMA table_info(debts)")
                columns = [row[1] for row in cursor.fetchall()]
                if 'order_id' not in columns:
                    print("Updating debts table schema...")
                    # Drop and recreate debts table with new schema
                    conn.execute("DROP TABLE IF EXISTS debts")
                    conn.execute('''
                        CREATE TABLE debts (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            customer_id INTEGER NOT NULL,
                            order_id INTEGER,
                            amount REAL NOT NULL,
                            payment REAL DEFAULT 0,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (customer_id) REFERENCES customers (id),
                            FOREIGN KEY (order_id) REFERENCES orders (id)
                        )
                    ''')
                    conn.commit()
                    print("Debts table updated with new schema")
                else:
                    # Tables exist with correct schema, clear debts table to fix incorrect data
                    print("Clearing debts table to fix calculation issue...")
                    conn.execute("DELETE FROM debts")
                    conn.commit()
                return

            print("Creating database tables...")

            # Create fresh tables
            conn.execute('''
                CREATE TABLE customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT NOT NULL,
                    chest REAL,
                    shoulder REAL,
                    hand REAL,
                    waist REAL,
                    hip REAL,
                    inseam REAL,
                    neck REAL,
                    height REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.execute('''
                CREATE TABLE orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER NOT NULL,
                    order_type TEXT NOT NULL,
                    total_price REAL NOT NULL,
                    payment REAL NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            ''')

            conn.execute('''
                CREATE TABLE debts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    customer_id INTEGER NOT NULL,
                    order_id INTEGER,
                    amount REAL NOT NULL,
                    payment REAL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (order_id) REFERENCES orders (id)
                )
            ''')

            conn.commit()
            print("Database tables created successfully")
        except Exception as e:
            print(f"Error creating tables: {e}")

    def get_customers(self):
        """Get all customers."""
        conn = self.get_db_connection()
        try:
            cursor = conn.execute("""
                SELECT id, name, phone, chest, shoulder, hand, waist, hip,
                       inseam, neck, height, created_at
                FROM customers
                ORDER BY name
            """)
            customers = []
            for row in cursor.fetchall():
                customers.append({
                    "id": row["id"],
                    "name": row["name"],
                    "phone": row["phone"],
                    "chest": row["chest"],
                    "shoulder": row["shoulder"],
                    "hand": row["hand"],
                    "waist": row["waist"],
                    "hip": row["hip"],
                    "inseam": row["inseam"],
                    "neck": row["neck"],
                    "height": row["height"],
                    "created_at": row["created_at"]
                })
            return customers
        finally:
            conn.close()

    def get_customer_by_id(self, customer_id):
        """Get a single customer by ID."""
        conn = self.get_db_connection()
        try:
            cursor = conn.execute("""
                SELECT id, name, phone, chest, shoulder, hand, waist, hip,
                       inseam, neck, height, created_at
                FROM customers
                WHERE id = ?
            """, (customer_id,))
            row = cursor.fetchone()

            if row:
                return {
                    "id": row["id"],
                    "name": row["name"],
                    "phone": row["phone"],
                    "chest": row["chest"],
                    "shoulder": row["shoulder"],
                    "hand": row["hand"],
                    "waist": row["waist"],
                    "hip": row["hip"],
                    "inseam": row["inseam"],
                    "neck": row["neck"],
                    "height": row["height"],
                    "created_at": row["created_at"]
                }
            return None
        finally:
            conn.close()

    def create_customer(self, data):
        """Create a new customer."""
        conn = self.get_db_connection()
        try:
            cursor = conn.execute("""
                INSERT INTO customers (name, phone, chest, shoulder, hand, waist, hip, inseam, neck, height)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                data.get('name'), data.get('phone'), data.get('chest'), data.get('shoulder'),
                data.get('hand'), data.get('waist'), data.get('hip'), data.get('inseam'),
                data.get('neck'), data.get('height')
            ))
            conn.commit()

            customer_id = cursor.lastrowid
            cursor = conn.execute("""
                SELECT id, name, phone, chest, shoulder, hand, waist, hip,
                       inseam, neck, height, created_at
                FROM customers WHERE id = ?
            """, (customer_id,))
            row = cursor.fetchone()

            return {
                "id": row["id"],
                "name": row["name"],
                "phone": row["phone"],
                "chest": row["chest"],
                "shoulder": row["shoulder"],
                "hand": row["hand"],
                "waist": row["waist"],
                "hip": row["hip"],
                "inseam": row["inseam"],
                "neck": row["neck"],
                "height": row["height"],
                "created_at": row["created_at"]
            }
        finally:
            conn.close()

    def update_customer(self, customer_id, data):
        """Update a customer."""
        conn = self.get_db_connection()
        try:
            # Check if customer exists
            cursor = conn.execute("SELECT id FROM customers WHERE id = ?", (customer_id,))
            if not cursor.fetchone():
                return None

            # Update customer
            conn.execute("""
                UPDATE customers
                SET name = ?, phone = ?, chest = ?, shoulder = ?, hand = ?,
                    waist = ?, hip = ?, inseam = ?, neck = ?, height = ?
                WHERE id = ?
            """, (
                data.get('name'), data.get('phone'), data.get('chest'), data.get('shoulder'),
                data.get('hand'), data.get('waist'), data.get('hip'), data.get('inseam'),
                data.get('neck'), data.get('height'), customer_id
            ))
            conn.commit()

            # Return updated customer
            cursor = conn.execute("""
                SELECT id, name, phone, chest, shoulder, hand, waist, hip,
                       inseam, neck, height, created_at
                FROM customers WHERE id = ?
            """, (customer_id,))
            row = cursor.fetchone()

            return {
                "id": row["id"],
                "name": row["name"],
                "phone": row["phone"],
                "chest": row["chest"],
                "shoulder": row["shoulder"],
                "hand": row["hand"],
                "waist": row["waist"],
                "hip": row["hip"],
                "inseam": row["inseam"],
                "neck": row["neck"],
                "height": row["height"],
                "created_at": row["created_at"]
            }
        finally:
            conn.close()

    def delete_customer(self, customer_id):
        """Delete a customer and all related orders and debts."""
        conn = self.get_db_connection()
        try:
            # Check if customer exists
            cursor = conn.execute("SELECT id, name FROM customers WHERE id = ?", (customer_id,))
            customer = cursor.fetchone()
            if not customer:
                return None

            # Delete related debts first
            conn.execute("DELETE FROM debts WHERE customer_id = ?", (customer_id,))

            # Delete related orders
            conn.execute("DELETE FROM orders WHERE customer_id = ?", (customer_id,))

            # Delete customer
            conn.execute("DELETE FROM customers WHERE id = ?", (customer_id,))

            conn.commit()

            return {
                "message": f"Customer {customer['name']} and all related data deleted successfully",
                "deleted_customer_id": customer_id
            }
        finally:
            conn.close()

    def get_orders(self):
        """Get all orders."""
        conn = self.get_db_connection()
        try:
            cursor = conn.execute("""
                SELECT o.id, o.customer_id, o.order_type, o.total_price, o.payment,
                       (o.total_price - o.payment) as remaining, o.created_at
                FROM orders o
                ORDER BY o.created_at DESC
            """)
            orders = []
            for row in cursor.fetchall():
                orders.append({
                    "id": row["id"],
                    "customer_id": row["customer_id"],
                    "order_type": row["order_type"],
                    "total_price": row["total_price"],
                    "payment": row["payment"],
                    "remaining": row["remaining"],
                    "created_at": row["created_at"]
                })
            return orders
        finally:
            conn.close()

    def create_order(self, data):
        """Create a new order."""
        conn = self.get_db_connection()
        try:
            cursor = conn.execute("""
                INSERT INTO orders (customer_id, order_type, total_price, payment)
                VALUES (?, ?, ?, ?)
            """, (data.get('customer_id'), data.get('order_type'), data.get('total_price'), data.get('payment')))
            conn.commit()

            order_id = cursor.lastrowid
            cursor = conn.execute("""
                SELECT id, customer_id, order_type, total_price, payment,
                       (total_price - payment) as remaining, created_at
                FROM orders WHERE id = ?
            """, (order_id,))
            row = cursor.fetchone()

            # Create debt if there's remaining balance
            total_price = data.get('total_price', 0)
            payment_made = data.get('payment', 0)
            remaining = total_price - payment_made

            if remaining > 0:
                conn.execute("""
                    INSERT INTO debts (customer_id, order_id, amount, payment)
                    VALUES (?, ?, ?, ?)
                """, (data.get('customer_id'), order_id, total_price, payment_made))
                conn.commit()
                print(f"Created debt: Customer {data.get('customer_id')}, Order {order_id}, Amount {total_price}, Paid {payment_made}, Remaining {remaining}")

            return {
                "id": row["id"],
                "customer_id": row["customer_id"],
                "order_type": row["order_type"],
                "total_price": row["total_price"],
                "payment": row["payment"],
                "remaining": row["remaining"],
                "created_at": row["created_at"]
            }
        finally:
            conn.close()

    def get_debts(self):
        """Get all debts with order information."""
        conn = self.get_db_connection()
        try:
            cursor = conn.execute("""
                SELECT d.id, d.customer_id, d.order_id, d.amount, d.payment,
                       (d.amount - d.payment) as remaining, d.created_at,
                       o.order_type, o.total_price as order_total
                FROM debts d
                LEFT JOIN orders o ON d.order_id = o.id
                WHERE (d.amount - d.payment) > 0
                ORDER BY d.created_at DESC
            """)
            debts = []
            for row in cursor.fetchall():
                debts.append({
                    "id": row["id"],
                    "customer_id": row["customer_id"],
                    "order_id": row["order_id"],
                    "amount": row["amount"],
                    "payment": row["payment"],
                    "remaining": row["remaining"],
                    "created_at": row["created_at"],
                    "order_type": row["order_type"],
                    "order_total": row["order_total"]
                })
            return debts
        finally:
            conn.close()

    def get_daily_statistics(self):
        """Get daily statistics."""
        conn = self.get_db_connection()
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            cursor = conn.execute("""
                SELECT
                    COUNT(*) as total_orders,
                    COALESCE(SUM(total_price), 0) as total_amount,
                    COALESCE(SUM(payment), 0) as paid_amount,
                    COALESCE(SUM(total_price - payment), 0) as remaining_amount
                FROM orders
                WHERE DATE(created_at) = ?
            """, (today,))
            row = cursor.fetchone()

            return {
                "total_orders": row["total_orders"],
                "total_amount": row["total_amount"],
                "paid_amount": row["paid_amount"],
                "remaining_amount": row["remaining_amount"]
            }
        finally:
            conn.close()

    def get_monthly_statistics(self):
        """Get monthly statistics."""
        conn = self.get_db_connection()
        try:
            current_month = datetime.now().strftime('%Y-%m')
            cursor = conn.execute("""
                SELECT
                    COUNT(*) as total_orders,
                    COALESCE(SUM(total_price), 0) as total_amount,
                    COALESCE(SUM(payment), 0) as paid_amount,
                    COALESCE(SUM(total_price - payment), 0) as remaining_amount
                FROM orders
                WHERE strftime('%Y-%m', created_at) = ?
            """, (current_month,))
            row = cursor.fetchone()

            return {
                "total_orders": row["total_orders"],
                "total_amount": row["total_amount"],
                "paid_amount": row["paid_amount"],
                "remaining_amount": row["remaining_amount"]
            }
        finally:
            conn.close()

    def search_customers(self, query):
        """Search customers by name or phone."""
        conn = self.get_db_connection()
        try:
            cursor = conn.execute("""
                SELECT id, name, phone, chest, shoulder, hand, waist, hip,
                       inseam, neck, height, created_at
                FROM customers
                WHERE name LIKE ? OR phone LIKE ?
                ORDER BY name
            """, (f"%{query}%", f"%{query}%"))

            customers = []
            for row in cursor.fetchall():
                customers.append({
                    "id": row["id"],
                    "name": row["name"],
                    "phone": row["phone"],
                    "chest": row["chest"],
                    "shoulder": row["shoulder"],
                    "hand": row["hand"],
                    "waist": row["waist"],
                    "hip": row["hip"],
                    "inseam": row["inseam"],
                    "neck": row["neck"],
                    "height": row["height"],
                    "created_at": row["created_at"]
                })
            return customers
        finally:
            conn.close()

def run_server():
    """Run the HTTP server - Desktop Only Mode."""
    # Try to find an available port starting from 8000
    for port in range(8000, 8010):
        try:
            # Bind only to localhost (127.0.0.1) for desktop-only access
            with socketserver.TCPServer(("127.0.0.1", port), DesktopAPIHandler) as httpd:
                print(f"Ma'aris Al Jahra Desktop API Server running on http://localhost:{port}")
                print("*** DESKTOP ONLY MODE - Not accessible from external browsers ***")
                print(f"Database: {DB_PATH}")
                print("Server is ready!")
                httpd.serve_forever()
                break
        except OSError:
            continue
    else:
        print("Could not find an available port")

if __name__ == "__main__":
    run_server()
