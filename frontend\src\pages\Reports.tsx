import React from 'react';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import { 
  BarChart3, 
  Calendar, 
  TrendingUp, 
  DollarSign,
  ShoppingCart,
  CreditCard
} from 'lucide-react';
import { fetchDailyStatistics, fetchMonthlyStatistics, fetchOrders } from '../services/api';

const Reports: React.FC = () => {
  const { data: dailyStats, isLoading: dailyLoading } = useQuery(
    'dailyStatistics',
    fetchDailyStatistics
  );
  const { data: monthlyStats, isLoading: monthlyLoading } = useQuery(
    'monthlyStatistics',
    fetchMonthlyStatistics
  );
  const { data: orders } = useQuery('orders', fetchOrders);

  if (dailyLoading || monthlyLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="spinner"></div>
      </div>
    );
  }

  const todayOrders = orders?.filter(order => {
    const today = new Date().toDateString();
    const orderDate = new Date(order.created_at).toDateString();
    return today === orderDate;
  }) || [];

  const thisMonthOrders = orders?.filter(order => {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const orderDate = new Date(order.created_at);
    return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear;
  }) || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-3xl font-bold text-gray-800 flex items-center">
          <BarChart3 className="w-8 h-8 ml-3" />
          التقارير والإحصائيات
        </h1>
        <p className="text-gray-600 mt-1">تقارير مفصلة عن الأداء والمبيعات</p>
      </motion.div>

      {/* Daily Report */}
      <motion.section
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
            <Calendar className="w-5 h-5 ml-2" />
            تقرير طلبات اليوم
          </h2>
          
          {/* Daily Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-600 text-sm font-medium">عدد الطلبات</p>
                  <p className="text-2xl font-bold text-blue-800">{dailyStats?.total_orders || 0}</p>
                </div>
                <ShoppingCart className="w-8 h-8 text-blue-500" />
              </div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-600 text-sm font-medium">إجمالي المبيعات</p>
                  <p className="text-2xl font-bold text-green-800">{dailyStats?.total_amount || 0} د.ك</p>
                </div>
                <DollarSign className="w-8 h-8 text-green-500" />
              </div>
            </div>
            
            <div className="bg-primary-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-primary-600 text-sm font-medium">المبلغ المدفوع</p>
                  <p className="text-2xl font-bold text-primary-800">{dailyStats?.paid_amount || 0} د.ك</p>
                </div>
                <TrendingUp className="w-8 h-8 text-primary-500" />
              </div>
            </div>
            
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-red-600 text-sm font-medium">المبلغ المتبقي</p>
                  <p className="text-2xl font-bold text-red-800">{dailyStats?.remaining_amount || 0} د.ك</p>
                </div>
                <CreditCard className="w-8 h-8 text-red-500" />
              </div>
            </div>
          </div>

          {/* Daily Orders List */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">قائمة طلبات اليوم</h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {todayOrders.map((order) => (
                <div key={order.id} className="bg-gray-50 p-3 rounded-lg flex justify-between items-center">
                  <div>
                    <span className="font-medium">طلب #{order.id}</span>
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${
                      order.order_type === 'صيفي' 
                        ? 'bg-yellow-100 text-yellow-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {order.order_type}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    <span className="font-semibold">{order.total_price} د.ك</span>
                  </div>
                </div>
              ))}
              {todayOrders.length === 0 && (
                <p className="text-gray-500 text-center py-4">لا توجد طلبات اليوم</p>
              )}
            </div>
          </div>
        </div>
      </motion.section>

      {/* Monthly Report */}
      <motion.section
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
            <Calendar className="w-5 h-5 ml-2" />
            تقرير طلبات الشهر
          </h2>
          
          {/* Monthly Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-600 text-sm font-medium">عدد الطلبات</p>
                  <p className="text-2xl font-bold text-purple-800">{monthlyStats?.total_orders || 0}</p>
                </div>
                <ShoppingCart className="w-8 h-8 text-purple-500" />
              </div>
            </div>
            
            <div className="bg-indigo-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-indigo-600 text-sm font-medium">إجمالي المبيعات</p>
                  <p className="text-2xl font-bold text-indigo-800">{monthlyStats?.total_amount || 0} د.ك</p>
                </div>
                <DollarSign className="w-8 h-8 text-indigo-500" />
              </div>
            </div>
            
            <div className="bg-pink-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-pink-600 text-sm font-medium">المبلغ المدفوع</p>
                  <p className="text-2xl font-bold text-pink-800">{monthlyStats?.paid_amount || 0} د.ك</p>
                </div>
                <TrendingUp className="w-8 h-8 text-pink-500" />
              </div>
            </div>
            
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-600 text-sm font-medium">المبلغ المتبقي</p>
                  <p className="text-2xl font-bold text-orange-800">{monthlyStats?.remaining_amount || 0} د.ك</p>
                </div>
                <CreditCard className="w-8 h-8 text-orange-500" />
              </div>
            </div>
          </div>

          {/* Monthly Orders List */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">قائمة طلبات الشهر</h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {thisMonthOrders.map((order) => (
                <div key={order.id} className="bg-gray-50 p-3 rounded-lg flex justify-between items-center">
                  <div>
                    <span className="font-medium">طلب #{order.id}</span>
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${
                      order.order_type === 'صيفي' 
                        ? 'bg-yellow-100 text-yellow-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {order.order_type}
                    </span>
                    <span className="ml-2 text-xs text-gray-500">
                      {new Date(order.created_at).toLocaleDateString('ar-SA')}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    <span className="font-semibold">{order.total_price} د.ك</span>
                  </div>
                </div>
              ))}
              {thisMonthOrders.length === 0 && (
                <p className="text-gray-500 text-center py-4">لا توجد طلبات هذا الشهر</p>
              )}
            </div>
          </div>
        </div>
      </motion.section>
    </div>
  );
};

export default Reports;
