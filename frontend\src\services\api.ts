import axios from 'axios';

// API base URL
const API_BASE_URL = 'http://localhost:8000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types
export interface Customer {
  id: number;
  name: string;
  phone: string;
  chest?: number;
  shoulder?: number;
  hand?: number;
  waist?: number;
  hip?: number;
  inseam?: number;
  neck?: number;
  height?: number;
  created_at: string;
}

export interface CustomerCreate {
  name: string;
  phone: string;
  chest?: number;
  shoulder?: number;
  hand?: number;
  waist?: number;
  hip?: number;
  inseam?: number;
  neck?: number;
  height?: number;
}

export interface Order {
  id: number;
  customer_id: number;
  order_type: string;
  total_price: number;
  payment: number;
  remaining: number;
  created_at: string;
}

export interface OrderCreate {
  customer_id: number;
  order_type: string;
  total_price: number;
  payment: number;
}

export interface Debt {
  id: number;
  customer_id: number;
  amount: number;
  payment: number;
  remaining: number;
  created_at: string;
}

export interface Statistics {
  total_orders: number;
  total_amount: number;
  paid_amount: number;
  remaining_amount: number;
}

// Customer API functions
export const fetchCustomers = async (): Promise<Customer[]> => {
  const response = await api.get('/customers');
  return response.data;
};

export const fetchCustomer = async (id: number): Promise<Customer> => {
  const response = await api.get(`/customers/${id}`);
  return response.data;
};

export const createCustomer = async (customer: CustomerCreate): Promise<Customer> => {
  const response = await api.post('/customers', customer);
  return response.data;
};

export const updateCustomer = async (id: number, customer: CustomerCreate): Promise<Customer> => {
  const response = await api.put(`/customers/${id}`, customer);
  return response.data;
};

export const deleteCustomer = async (id: number): Promise<void> => {
  await api.delete(`/customers/${id}`);
};

export const searchCustomers = async (query: string): Promise<Customer[]> => {
  const response = await api.get(`/customers/search/${query}`);
  return response.data;
};

// Order API functions
export const fetchOrders = async (): Promise<Order[]> => {
  const response = await api.get('/orders');
  return response.data;
};

export const createOrder = async (order: OrderCreate): Promise<Order> => {
  const response = await api.post('/orders', order);
  return response.data;
};

// Debt API functions
export const fetchDebts = async (): Promise<Debt[]> => {
  const response = await api.get('/debts');
  return response.data;
};

// Statistics API functions
export const fetchDailyStatistics = async (): Promise<Statistics> => {
  const response = await api.get('/statistics/daily');
  return response.data;
};

export const fetchMonthlyStatistics = async (): Promise<Statistics> => {
  const response = await api.get('/statistics/monthly');
  return response.data;
};

export default api;
