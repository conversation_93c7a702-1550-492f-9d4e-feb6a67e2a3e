import React from 'react';
import { NavLink } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Home, 
  Users, 
  ShoppingCart, 
  CreditCard, 
  BarChart3,
  ChevronLeft
} from 'lucide-react';

interface NavItem {
  path: string;
  label: string;
  icon: React.ReactNode;
}

const navItems: NavItem[] = [
  {
    path: '/',
    label: 'الرئيسية',
    icon: <Home className="w-5 h-5" />
  },
  {
    path: '/customers',
    label: 'العملاء',
    icon: <Users className="w-5 h-5" />
  },
  {
    path: '/orders',
    label: 'الطلبات',
    icon: <ShoppingCart className="w-5 h-5" />
  },
  {
    path: '/debts',
    label: 'الديون',
    icon: <CreditCard className="w-5 h-5" />
  },
  {
    path: '/reports',
    label: 'التقارير',
    icon: <BarChart3 className="w-5 h-5" />
  }
];

const Sidebar: React.FC = () => {
  return (
    <motion.aside 
      className="fixed right-0 top-20 h-screen w-64 bg-white shadow-xl z-40"
      initial={{ x: 300 }}
      animate={{ x: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="p-6">
        <nav className="space-y-2">
          {navItems.map((item, index) => (
            <motion.div
              key={item.path}
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <NavLink
                to={item.path}
                className={({ isActive }) =>
                  `flex items-center justify-between p-3 rounded-lg transition-all duration-200 group ${
                    isActive
                      ? 'bg-primary-400 text-white shadow-lg'
                      : 'text-gray-700 hover:bg-primary-50 hover:text-primary-600'
                  }`
                }
              >
                <div className="flex items-center space-x-3 space-x-reverse">
                  {item.icon}
                  <span className="font-medium text-arabic-lg">{item.label}</span>
                </div>
                <ChevronLeft className="w-4 h-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
              </NavLink>
            </motion.div>
          ))}
        </nav>
      </div>

      {/* Decorative element */}
      <motion.div 
        className="absolute bottom-6 right-6 left-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        <div className="bg-gradient-to-r from-primary-400 to-primary-600 rounded-lg p-4 text-white text-center">
          <p className="text-sm font-medium">نسخة 2.0</p>
          <p className="text-xs opacity-80">واجهة حديثة ومتطورة</p>
        </div>
      </motion.div>
    </motion.aside>
  );
};

export default Sidebar;
