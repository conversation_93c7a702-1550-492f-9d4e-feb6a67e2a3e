#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - الواجهة الجميلة المتطورة
Ma'aris Al <PERSON> - Beautiful Advanced Interface

تطبيق سطح مكتب جميل ومتطور لإدارة العملاء والطلبات والديون
Beautiful and modern desktop application for managing customers, orders, and debts
"""

import sys
import os
from datetime import datetime

try:
    from PySide6.QtWidgets import (
        QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
        QPushButton, QFrame, QLineEdit, QListWidget, QGridLayout,
        QComboBox, QTabWidget, QMessageBox, QApplication, QListWidgetItem,
        QScrollArea, QSplitter, QTextEdit, QSpacerItem, QSizePolicy,
        QFormLayout, QGroupBox, QProgressBar
    )
    from PySide6.QtCore import Qt, QTimer, Signal, QPropertyAnimation, QEasingCurve
    from PySide6.QtGui import QFont, QPixmap, QIcon, QPalette, QColor, QLinearGradient
    PYSIDE6_AVAILABLE = True
except ImportError:
    print("PySide6 is required for this application")
    sys.exit(1)

# Add parent directory to path to import database
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from database import Database

class BeautifulInterface(QMainWindow):
    """
    الواجهة الجميلة الرئيسية لمعاريس الجهراء
    Beautiful main interface for Ma'aris Al Jahra
    """

    def __init__(self):
        super().__init__()
        self.db = Database()
        self.current_customer_id = None
        self.current_order_id = None

        # Initialize UI
        self.init_ui()
        self.load_initial_data()

        # Setup auto-refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_all_data)
        self.refresh_timer.start(30000)  # Refresh every 30 seconds

    def init_ui(self):
        """Initialize the beautiful user interface."""
        self.setWindowTitle("معاريس الجهراء - نظام إدارة العملاء والطلبات")
        self.setGeometry(100, 100, 1600, 1000)
        self.setMinimumSize(1400, 900)

        # Set application icon
        self.setWindowIcon(QIcon("assets/icon.png") if os.path.exists("assets/icon.png") else QIcon())

        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Apply beautiful stylesheet
        self.apply_beautiful_styles()

        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create header
        self.create_header(main_layout)

        # Create main content area
        self.create_main_content(main_layout)

        # Create status bar
        self.create_status_bar()

        # Center window on screen
        self.center_on_screen()

    def apply_beautiful_styles(self):
        """Apply beautiful modern styles to the application."""
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #f8f9fa, stop: 0.5 #e9ecef, stop: 1 #dee2e6);
            }

            QWidget {
                font-family: "Segoe UI", "Traditional Arabic", "Tahoma", "Arial";
                font-size: 14px;
                color: #2c3e50;
            }

            /* Header Styles */
            #headerFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #667eea, stop: 0.5 #764ba2, stop: 1 #f093fb);
                border: none;
                border-radius: 0px;
                padding: 20px;
                min-height: 80px;
            }

            #logoLabel {
                color: white;
                font-size: 32px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }

            #statsLabel {
                color: white;
                font-size: 16px;
                background-color: rgba(255,255,255,0.2);
                border-radius: 10px;
                padding: 10px 15px;
                margin: 5px;
            }

            /* Tab Widget Styles */
            QTabWidget::pane {
                border: 2px solid #3498db;
                border-radius: 15px;
                background-color: white;
                margin-top: 10px;
            }

            QTabBar::tab {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ecf0f1, stop: 1 #bdc3c7);
                border: 2px solid #95a5a6;
                border-bottom: none;
                border-top-left-radius: 15px;
                border-top-right-radius: 15px;
                padding: 15px 25px;
                margin-right: 5px;
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                min-width: 120px;
            }

            QTabBar::tab:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border-color: #2980b9;
            }

            QTabBar::tab:hover:!selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5dade2, stop: 1 #3498db);
                color: white;
            }

            /* Button Styles */
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                padding: 12px 20px;
                border-radius: 10px;
                border: 2px solid transparent;
                min-width: 100px;
                min-height: 20px;
            }

            .primaryButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border-color: #2471a3;
            }

            .primaryButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #5dade2, stop: 1 #3498db);
            }

            .successButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #27ae60, stop: 1 #229954);
                color: white;
                border-color: #1e8449;
            }

            .successButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #58d68d, stop: 1 #27ae60);
            }

            .dangerButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #e74c3c, stop: 1 #c0392b);
                color: white;
                border-color: #a93226;
            }

            .dangerButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ec7063, stop: 1 #e74c3c);
            }

            .warningButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f39c12, stop: 1 #e67e22);
                color: white;
                border-color: #d68910;
            }

            .warningButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #f4d03f, stop: 1 #f39c12);
            }

            /* Input Styles */
            QLineEdit {
                font-size: 16px;
                padding: 12px 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
            }

            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
                box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
            }

            QComboBox {
                font-size: 16px;
                padding: 12px 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
            }

            QComboBox:focus {
                border-color: #3498db;
            }

            /* List Widget Styles */
            QListWidget {
                font-size: 16px;
                border: 2px solid #bdc3c7;
                border-radius: 10px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                padding: 5px;
            }

            QListWidget::item {
                padding: 15px;
                border-bottom: 1px solid #ecf0f1;
                border-radius: 5px;
                margin: 2px;
            }

            QListWidget::item:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border-radius: 8px;
            }

            QListWidget::item:hover {
                background-color: #e8f4fd;
                border-radius: 8px;
            }

            /* Group Box Styles */
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: white;
            }

            /* Label Styles */
            QLabel {
                font-size: 16px;
                color: #2c3e50;
                font-weight: bold;
            }

            .titleLabel {
                font-size: 20px;
                color: #3498db;
                font-weight: bold;
                margin: 10px 0;
            }

            .errorLabel {
                color: #e74c3c;
                font-weight: bold;
            }

            .successLabel {
                color: #27ae60;
                font-weight: bold;
            }
        """)

    def create_header(self, main_layout):
        """Create beautiful header with logo and statistics."""
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(30, 20, 30, 20)

        # Logo
        logo_label = QLabel("🏪 معاريس الجهراء")
        logo_label.setObjectName("logoLabel")
        logo_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        # Statistics
        self.stats_layout = QHBoxLayout()
        self.update_header_stats()

        header_layout.addWidget(logo_label)
        header_layout.addStretch()
        header_layout.addLayout(self.stats_layout)

        main_layout.addWidget(header_frame)

    def update_header_stats(self):
        """Update header statistics."""
        # Clear existing stats
        while self.stats_layout.count():
            child = self.stats_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # Get statistics
        daily_stats = self.db.get_daily_orders_total()
        monthly_stats = self.db.get_monthly_orders_total()
        total_customers = len(self.db.get_all_customers())

        # Create stat labels
        stats = [
            ("👥 العملاء", str(total_customers)),
            ("📅 اليوم", f"{daily_stats['total']:.1f} د.ك"),
            ("📊 الشهر", f"{monthly_stats['total']:.1f} د.ك"),
            ("💰 المتبقي", f"{monthly_stats['remaining']:.1f} د.ك")
        ]

        for title, value in stats:
            stat_label = QLabel(f"{title}\n{value}")
            stat_label.setObjectName("statsLabel")
            stat_label.setAlignment(Qt.AlignCenter)
            self.stats_layout.addWidget(stat_label)

    def create_main_content(self, main_layout):
        """Create main content area with tabs."""
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)

        # Create tabs
        self.create_customers_tab()
        self.create_orders_tab()
        self.create_debts_tab()
        self.create_reports_tab()

        main_layout.addWidget(self.tab_widget)

    def create_status_bar(self):
        """Create status bar."""
        status_bar = self.statusBar()
        status_bar.showMessage("جاهز - Ready")
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #34495e;
                color: white;
                font-weight: bold;
                padding: 5px;
            }
        """)

    def center_on_screen(self):
        """Center window on screen."""
        try:
            screen = QApplication.primaryScreen().geometry()
            x = (screen.width() - self.width()) // 2
            y = (screen.height() - self.height()) // 2
            self.move(x, y)
        except:
            pass

    def load_initial_data(self):
        """Load initial data for all tabs."""
        self.refresh_all_data()

    def refresh_all_data(self):
        """Refresh all data in the application."""
        self.update_header_stats()
        self.load_customers()
        self.load_orders()
        self.load_debts()
        self.load_reports()

        # Update status
        self.statusBar().showMessage(f"آخر تحديث: {datetime.now().strftime('%H:%M:%S')}")

    def create_customers_tab(self):
        """Create beautiful customers management tab."""
        customers_widget = QWidget()
        layout = QHBoxLayout(customers_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Left panel - Customer list and search
        left_panel = QFrame()
        left_panel.setMaximumWidth(400)
        left_panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #3498db;
                border-radius: 15px;
                padding: 15px;
            }
        """)

        left_layout = QVBoxLayout(left_panel)

        # Search section
        search_group = QGroupBox("🔍 البحث عن العملاء")
        search_layout = QVBoxLayout(search_group)

        self.customer_search = QLineEdit()
        self.customer_search.setPlaceholderText("ابحث بالاسم أو رقم الهاتف...")
        self.customer_search.textChanged.connect(self.search_customers)
        search_layout.addWidget(self.customer_search)

        # Customer list
        customers_group = QGroupBox("👥 قائمة العملاء")
        customers_layout = QVBoxLayout(customers_group)

        self.customers_list = QListWidget()
        self.customers_list.itemClicked.connect(self.on_customer_selected)
        customers_layout.addWidget(self.customers_list)

        left_layout.addWidget(search_group)
        left_layout.addWidget(customers_group)

        # Right panel - Customer details form
        right_panel = QFrame()
        right_panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #27ae60;
                border-radius: 15px;
                padding: 15px;
            }
        """)

        right_layout = QVBoxLayout(right_panel)

        # Customer form
        form_group = QGroupBox("📝 بيانات العميل")
        form_layout = QFormLayout(form_group)
        form_layout.setSpacing(15)

        # Basic info
        self.customer_name = QLineEdit()
        self.customer_name.setPlaceholderText("اسم العميل...")
        form_layout.addRow("الاسم:", self.customer_name)

        self.customer_phone = QLineEdit()
        self.customer_phone.setPlaceholderText("رقم الهاتف...")
        form_layout.addRow("الهاتف:", self.customer_phone)

        # Measurements section
        measurements_group = QGroupBox("📏 القياسات")
        measurements_layout = QGridLayout(measurements_group)

        # Create measurement fields
        self.measurements = {}
        measurement_fields = [
            ("الصدر", "chest"), ("الكتف", "shoulder"), ("اليد", "hand"), ("الخصر", "waist"),
            ("الورك", "hip"), ("طول الساق", "inseam"), ("الرقبة", "neck"), ("الطول", "height")
        ]

        for i, (label, field) in enumerate(measurement_fields):
            row, col = i // 2, (i % 2) * 2

            label_widget = QLabel(f"{label}:")
            field_widget = QLineEdit()
            field_widget.setPlaceholderText("0.0")

            measurements_layout.addWidget(label_widget, row, col)
            measurements_layout.addWidget(field_widget, row, col + 1)

            self.measurements[field] = field_widget

        # Buttons
        buttons_layout = QHBoxLayout()

        self.add_customer_btn = QPushButton("➕ إضافة عميل")
        self.add_customer_btn.setProperty("class", "successButton")
        self.add_customer_btn.clicked.connect(self.add_customer)

        self.edit_customer_btn = QPushButton("✏️ تعديل")
        self.edit_customer_btn.setProperty("class", "primaryButton")
        self.edit_customer_btn.clicked.connect(self.edit_customer)

        self.delete_customer_btn = QPushButton("🗑️ حذف")
        self.delete_customer_btn.setProperty("class", "dangerButton")
        self.delete_customer_btn.clicked.connect(self.delete_customer)

        self.save_customer_btn = QPushButton("💾 حفظ")
        self.save_customer_btn.setProperty("class", "warningButton")
        self.save_customer_btn.clicked.connect(self.save_customer)

        buttons_layout.addWidget(self.add_customer_btn)
        buttons_layout.addWidget(self.edit_customer_btn)
        buttons_layout.addWidget(self.delete_customer_btn)
        buttons_layout.addWidget(self.save_customer_btn)

        right_layout.addWidget(form_group)
        right_layout.addWidget(measurements_group)
        right_layout.addLayout(buttons_layout)
        right_layout.addStretch()

        layout.addWidget(left_panel)
        layout.addWidget(right_panel)

        self.tab_widget.addTab(customers_widget, "👥 العملاء")

    def create_orders_tab(self):
        """Create beautiful orders management tab."""
        orders_widget = QWidget()
        layout = QHBoxLayout(orders_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Left panel - Customer selection
        left_panel = QFrame()
        left_panel.setMaximumWidth(350)
        left_panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #e67e22;
                border-radius: 15px;
                padding: 15px;
            }
        """)

        left_layout = QVBoxLayout(left_panel)

        # Customer search for orders
        search_group = QGroupBox("🔍 اختيار العميل")
        search_layout = QVBoxLayout(search_group)

        self.order_customer_search = QLineEdit()
        self.order_customer_search.setPlaceholderText("ابحث عن العميل...")
        self.order_customer_search.textChanged.connect(self.search_customers_for_order)
        search_layout.addWidget(self.order_customer_search)

        self.order_customers_list = QListWidget()
        self.order_customers_list.itemClicked.connect(self.on_order_customer_selected)
        search_layout.addWidget(self.order_customers_list)

        left_layout.addWidget(search_group)

        # Customer orders list
        orders_group = QGroupBox("📋 طلبات العميل")
        orders_layout = QVBoxLayout(orders_group)

        self.customer_orders_list = QListWidget()
        self.customer_orders_list.itemClicked.connect(self.on_order_selected)
        orders_layout.addWidget(self.customer_orders_list)

        left_layout.addWidget(orders_group)

        # Right panel - Order form
        right_panel = QFrame()
        right_panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #8e44ad;
                border-radius: 15px;
                padding: 15px;
            }
        """)

        right_layout = QVBoxLayout(right_panel)

        # Order form
        order_form_group = QGroupBox("📝 بيانات الطلب")
        order_form_layout = QFormLayout(order_form_group)
        order_form_layout.setSpacing(15)

        # Selected customer display
        self.selected_customer_label = QLabel("لم يتم اختيار عميل")
        self.selected_customer_label.setProperty("class", "titleLabel")
        order_form_layout.addRow("العميل المختار:", self.selected_customer_label)

        # Order type
        self.order_type = QComboBox()
        self.order_type.addItems(["صيفي", "شتوي", "رسمي", "كاجوال"])
        order_form_layout.addRow("نوع الطلب:", self.order_type)

        # Prices
        self.order_total_price = QLineEdit()
        self.order_total_price.setPlaceholderText("0.000")
        self.order_total_price.textChanged.connect(self.calculate_remaining)
        order_form_layout.addRow("السعر الإجمالي:", self.order_total_price)

        self.order_payment = QLineEdit()
        self.order_payment.setPlaceholderText("0.000")
        self.order_payment.textChanged.connect(self.calculate_remaining)
        order_form_layout.addRow("المدفوع:", self.order_payment)

        self.order_remaining_label = QLabel("0.000 د.ك")
        self.order_remaining_label.setProperty("class", "errorLabel")
        order_form_layout.addRow("المتبقي:", self.order_remaining_label)

        # Order buttons
        order_buttons_layout = QHBoxLayout()

        self.add_order_btn = QPushButton("➕ إضافة طلب")
        self.add_order_btn.setProperty("class", "successButton")
        self.add_order_btn.clicked.connect(self.add_order)

        self.edit_order_btn = QPushButton("✏️ تعديل")
        self.edit_order_btn.setProperty("class", "primaryButton")
        self.edit_order_btn.clicked.connect(self.edit_order)

        self.delete_order_btn = QPushButton("🗑️ حذف")
        self.delete_order_btn.setProperty("class", "dangerButton")
        self.delete_order_btn.clicked.connect(self.delete_order)

        self.save_order_btn = QPushButton("💾 حفظ")
        self.save_order_btn.setProperty("class", "warningButton")
        self.save_order_btn.clicked.connect(self.save_order)

        order_buttons_layout.addWidget(self.add_order_btn)
        order_buttons_layout.addWidget(self.edit_order_btn)
        order_buttons_layout.addWidget(self.delete_order_btn)
        order_buttons_layout.addWidget(self.save_order_btn)

        right_layout.addWidget(order_form_group)
        right_layout.addLayout(order_buttons_layout)
        right_layout.addStretch()

        layout.addWidget(left_panel)
        layout.addWidget(right_panel)

        self.tab_widget.addTab(orders_widget, "📋 الطلبات")

    def create_debts_tab(self):
        """Create beautiful debts management tab."""
        debts_widget = QWidget()
        layout = QVBoxLayout(debts_widget)
        layout.setContentsMargins(20, 20, 20, 20)

        # Debts list
        debts_group = QGroupBox("💰 قائمة الديون")
        debts_layout = QVBoxLayout(debts_group)

        self.debts_list = QListWidget()
        debts_layout.addWidget(self.debts_list)

        layout.addWidget(debts_group)
        self.tab_widget.addTab(debts_widget, "💰 الديون")

    def create_reports_tab(self):
        """Create beautiful reports tab."""
        reports_widget = QWidget()
        layout = QHBoxLayout(reports_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # Daily reports
        daily_panel = QFrame()
        daily_panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #3498db, stop: 1 #2980b9);
                border-radius: 15px;
                padding: 20px;
            }
        """)

        daily_layout = QVBoxLayout(daily_panel)

        daily_title = QLabel("📅 تقرير اليوم")
        daily_title.setStyleSheet("color: white; font-size: 24px; font-weight: bold; margin-bottom: 15px;")
        daily_title.setAlignment(Qt.AlignCenter)
        daily_layout.addWidget(daily_title)

        self.daily_stats_label = QLabel()
        self.daily_stats_label.setStyleSheet("color: white; font-size: 16px; background: rgba(255,255,255,0.2); border-radius: 10px; padding: 15px;")
        daily_layout.addWidget(self.daily_stats_label)

        self.daily_orders_list = QListWidget()
        self.daily_orders_list.setStyleSheet("background: white; border-radius: 10px; padding: 10px;")
        daily_layout.addWidget(self.daily_orders_list)

        # Monthly reports
        monthly_panel = QFrame()
        monthly_panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                    stop: 0 #e67e22, stop: 1 #d35400);
                border-radius: 15px;
                padding: 20px;
            }
        """)

        monthly_layout = QVBoxLayout(monthly_panel)

        monthly_title = QLabel("📊 تقرير الشهر")
        monthly_title.setStyleSheet("color: white; font-size: 24px; font-weight: bold; margin-bottom: 15px;")
        monthly_title.setAlignment(Qt.AlignCenter)
        monthly_layout.addWidget(monthly_title)

        self.monthly_stats_label = QLabel()
        self.monthly_stats_label.setStyleSheet("color: white; font-size: 16px; background: rgba(255,255,255,0.2); border-radius: 10px; padding: 15px;")
        monthly_layout.addWidget(self.monthly_stats_label)

        self.monthly_orders_list = QListWidget()
        self.monthly_orders_list.setStyleSheet("background: white; border-radius: 10px; padding: 10px;")
        monthly_layout.addWidget(self.monthly_orders_list)

        layout.addWidget(daily_panel)
        layout.addWidget(monthly_panel)

        self.tab_widget.addTab(reports_widget, "📊 التقارير")

    # Data loading methods
    def load_customers(self):
        """Load customers into the list."""
        self.customers_list.clear()
        customers = self.db.get_all_customers()

        for customer in customers:
            item_text = f"👤 {customer['name']}\n📞 {customer['phone']}"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, customer)
            self.customers_list.addItem(item)

    def load_orders(self):
        """Load orders for selected customer."""
        if not self.current_customer_id:
            return

        self.customer_orders_list.clear()
        orders = self.db.get_customer_orders(self.current_customer_id)

        for order in orders:
            item_text = f"📋 #{order['id']} - {order['order_type']}\n💰 {order['total_price']:.3f} د.ك (متبقي: {order['remaining']:.3f})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, order)
            self.customer_orders_list.addItem(item)

    def load_debts(self):
        """Load debts list."""
        self.debts_list.clear()
        # Implementation for debts loading
        pass

    def load_reports(self):
        """Load reports data."""
        # Daily reports
        daily_stats = self.db.get_daily_orders_total()
        daily_text = f"""
        📊 إحصائيات اليوم:
        💰 الإجمالي: {daily_stats['total']:.3f} د.ك
        ✅ المدفوع: {daily_stats['paid']:.3f} د.ك
        ❌ المتبقي: {daily_stats['remaining']:.3f} د.ك
        """
        self.daily_stats_label.setText(daily_text)

        # Load daily orders
        self.daily_orders_list.clear()
        daily_orders = self.db.get_daily_orders()
        for order in daily_orders:
            item_text = f"👤 {order['customer_name']} - {order['order_type']} - {order['total_price']:.3f} د.ك"
            self.daily_orders_list.addItem(item_text)

        # Monthly reports
        monthly_stats = self.db.get_monthly_orders_total()
        monthly_text = f"""
        📊 إحصائيات الشهر:
        💰 الإجمالي: {monthly_stats['total']:.3f} د.ك
        ✅ المدفوع: {monthly_stats['paid']:.3f} د.ك
        ❌ المتبقي: {monthly_stats['remaining']:.3f} د.ك
        """
        self.monthly_stats_label.setText(monthly_text)

        # Load monthly orders
        self.monthly_orders_list.clear()
        monthly_orders = self.db.get_monthly_orders()
        for order in monthly_orders:
            item_text = f"👤 {order['customer_name']} - {order['order_type']} - {order['total_price']:.3f} د.ك"
            self.monthly_orders_list.addItem(item_text)

    # Event handlers
    def search_customers(self):
        """Search customers by name or phone."""
        search_text = self.customer_search.text().strip()
        self.customers_list.clear()

        if search_text:
            customers = self.db.search_customers(search_text)
        else:
            customers = self.db.get_all_customers()

        for customer in customers:
            item_text = f"👤 {customer['name']}\n📞 {customer['phone']}"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, customer)
            self.customers_list.addItem(item)

    def search_customers_for_order(self):
        """Search customers for order creation."""
        search_text = self.order_customer_search.text().strip()
        self.order_customers_list.clear()

        if search_text:
            customers = self.db.search_customers(search_text)
        else:
            customers = self.db.get_all_customers()

        for customer in customers:
            item_text = f"👤 {customer['name']}\n📞 {customer['phone']}"
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, customer)
            self.order_customers_list.addItem(item)

    def on_customer_selected(self, item):
        """Handle customer selection."""
        if item:
            customer = item.data(Qt.UserRole)
            if customer:
                self.current_customer_id = customer['id']
                self.load_customer_form(customer)

    def on_order_customer_selected(self, item):
        """Handle customer selection for orders."""
        if item:
            customer = item.data(Qt.UserRole)
            if customer:
                self.current_customer_id = customer['id']
                self.selected_customer_label.setText(f"👤 {customer['name']} - 📞 {customer['phone']}")
                self.load_orders()

    def on_order_selected(self, item):
        """Handle order selection."""
        if item:
            order = item.data(Qt.UserRole)
            if order:
                self.current_order_id = order['id']
                self.load_order_form(order)

    def load_customer_form(self, customer):
        """Load customer data into form."""
        self.customer_name.setText(customer.get('name', ''))
        self.customer_phone.setText(customer.get('phone', ''))

        for field, widget in self.measurements.items():
            value = customer.get(field, 0)
            widget.setText(str(value) if value else '')

    def load_order_form(self, order):
        """Load order data into form."""
        self.order_type.setCurrentText(order.get('order_type', 'صيفي'))
        self.order_total_price.setText(str(order.get('total_price', '')))
        self.order_payment.setText(str(order.get('payment', '')))
        self.calculate_remaining()

    def calculate_remaining(self):
        """Calculate remaining amount."""
        try:
            total = float(self.order_total_price.text() or 0)
            payment = float(self.order_payment.text() or 0)
            remaining = total - payment

            self.order_remaining_label.setText(f"{remaining:.3f} د.ك")

            if remaining > 0:
                self.order_remaining_label.setProperty("class", "errorLabel")
            else:
                self.order_remaining_label.setProperty("class", "successLabel")

            # Refresh style
            self.order_remaining_label.style().unpolish(self.order_remaining_label)
            self.order_remaining_label.style().polish(self.order_remaining_label)

        except ValueError:
            self.order_remaining_label.setText("0.000 د.ك")

    # Business logic methods
    def add_customer(self):
        """Add new customer."""
        self.clear_customer_form()
        self.current_customer_id = None

    def edit_customer(self):
        """Edit selected customer."""
        if not self.current_customer_id:
            self.show_message("يرجى اختيار عميل للتعديل")

    def delete_customer(self):
        """Delete selected customer with fast response."""
        if not self.current_customer_id:
            self.show_message("يرجى اختيار عميل للحذف")
            return

        # Get customer info
        customer = None
        current_item = self.customers_list.currentItem()
        if current_item:
            customer = current_item.data(Qt.UserRole)

        if not customer:
            self.show_message("خطأ في بيانات العميل")
            return

        # Confirmation dialog
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف العميل '{customer['name']}'؟\n\nسيتم حذف جميع الطلبات والديون المرتبطة بهذا العميل.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Fast delete
                self.db.delete_customer(customer['id'])

                # Immediate UI update
                self.customers_list.takeItem(self.customers_list.row(current_item))
                self.clear_customer_form()
                self.current_customer_id = None

                # Background refresh
                QTimer.singleShot(100, self.refresh_all_data)

                self.show_success_message("تم حذف العميل بنجاح")

            except Exception as e:
                self.show_error_message(f"خطأ في حذف العميل: {str(e)}")

    def save_customer(self):
        """Save customer data."""
        name = self.customer_name.text().strip()
        phone = self.customer_phone.text().strip()

        if not name or not phone:
            self.show_error_message("يرجى ملء الاسم ورقم الهاتف")
            return

        # Prepare customer data
        customer_data = {
            'name': name,
            'phone': phone
        }

        # Add measurements
        for field, widget in self.measurements.items():
            try:
                value = float(widget.text()) if widget.text().strip() else 0
                customer_data[field] = value
            except ValueError:
                customer_data[field] = 0

        try:
            if self.current_customer_id:
                # Update existing customer
                customer_data['id'] = self.current_customer_id
                self.db.update_customer(customer_data)
                self.show_success_message("تم تحديث بيانات العميل بنجاح")
            else:
                # Add new customer
                self.db.add_customer(customer_data)
                self.show_success_message("تم إضافة العميل بنجاح")

            # Refresh data
            self.load_customers()
            self.search_customers_for_order()
            self.clear_customer_form()

        except Exception as e:
            self.show_error_message(f"خطأ في حفظ بيانات العميل: {str(e)}")

    def clear_customer_form(self):
        """Clear customer form."""
        self.customer_name.clear()
        self.customer_phone.clear()
        for widget in self.measurements.values():
            widget.clear()

    # Order methods
    def add_order(self):
        """Add new order."""
        self.clear_order_form()
        self.current_order_id = None

    def edit_order(self):
        """Edit selected order."""
        if not self.current_order_id:
            self.show_message("يرجى اختيار طلب للتعديل")

    def delete_order(self):
        """Delete selected order with fast response."""
        if not self.current_order_id:
            self.show_message("يرجى اختيار طلب للحذف")
            return

        # Get order info
        order = None
        current_item = self.customer_orders_list.currentItem()
        if current_item:
            order = current_item.data(Qt.UserRole)

        if not order:
            self.show_message("خطأ في بيانات الطلب")
            return

        # Confirmation dialog
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الطلب؟\n\nرقم الطلب: #{order['id']}\nنوع الطلب: {order['order_type']}\nالمبلغ: {order['total_price']:.3f} د.ك\n\nسيتم حذف الطلب والدين المرتبط به (إن وجد).",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Fast delete
                self.db.delete_order(order['id'])

                # Immediate UI update
                self.customer_orders_list.takeItem(self.customer_orders_list.row(current_item))
                self.clear_order_form()
                self.current_order_id = None

                # Background refresh
                QTimer.singleShot(100, self.refresh_all_data)

                self.show_success_message("تم حذف الطلب بنجاح")

            except Exception as e:
                self.show_error_message(f"خطأ في حذف الطلب: {str(e)}")

    def save_order(self):
        """Save order data."""
        if not self.current_customer_id:
            self.show_error_message("يرجى اختيار عميل")
            return

        try:
            total_price = float(self.order_total_price.text() or 0)
            payment = float(self.order_payment.text() or 0)
        except ValueError:
            self.show_error_message("يرجى إدخال أرقام صحيحة للمبالغ")
            return

        if total_price <= 0:
            self.show_error_message("يرجى إدخال سعر إجمالي صحيح")
            return

        if payment > total_price:
            self.show_error_message("المبلغ المدفوع لا يمكن أن يكون أكبر من السعر الإجمالي")
            return

        # Prepare order data
        order_data = {
            'customer_id': self.current_customer_id,
            'order_type': self.order_type.currentText(),
            'total_price': total_price,
            'payment': payment
        }

        try:
            if self.current_order_id:
                # Update existing order
                order_data['id'] = self.current_order_id
                self.db.update_order(order_data)
                self.show_success_message("تم تحديث الطلب بنجاح")
            else:
                # Add new order
                self.db.add_order(order_data)
                remaining = total_price - payment
                message = "تم إضافة الطلب بنجاح"
                if remaining > 0:
                    message += f"\nتم إنشاء دين بقيمة {remaining:.3f} د.ك"
                self.show_success_message(message)

            # Refresh data
            self.load_orders()
            self.clear_order_form()

        except Exception as e:
            self.show_error_message(f"خطأ في حفظ الطلب: {str(e)}")

    def clear_order_form(self):
        """Clear order form."""
        self.order_type.setCurrentIndex(0)
        self.order_total_price.clear()
        self.order_payment.clear()
        self.order_remaining_label.setText("0.000 د.ك")
        self.current_order_id = None

    # Utility methods
    def show_message(self, message):
        """Show information message."""
        QMessageBox.information(self, "معاريس الجهراء", message)

    def show_success_message(self, message):
        """Show success message."""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle("نجح العملية")
        msg.setText(message)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #d4edda;
                border: 2px solid #27ae60;
                border-radius: 10px;
            }
            QMessageBox QLabel {
                color: #155724;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        msg.exec()

    def show_error_message(self, message):
        """Show error message."""
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("خطأ")
        msg.setText(message)
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #f8d7da;
                border: 2px solid #e74c3c;
                border-radius: 10px;
            }
            QMessageBox QLabel {
                color: #721c24;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        msg.exec()

    def closeEvent(self, event):
        """Handle application close event."""
        reply = QMessageBox.question(
            self,
            "إغلاق التطبيق",
            "هل تريد إغلاق التطبيق؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Stop timer
            if hasattr(self, 'refresh_timer'):
                self.refresh_timer.stop()
            event.accept()
        else:
            event.ignore()
