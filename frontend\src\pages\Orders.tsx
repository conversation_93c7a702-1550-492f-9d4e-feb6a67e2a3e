import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { 
  ShoppingCart, 
  Plus, 
  Calendar,
  DollarSign,
  User,
  Package
} from 'lucide-react';
import { 
  fetchOrders, 
  fetchCustomers,
  createOrder,
  Order, 
  OrderCreate,
  Customer
} from '../services/api';

const Orders: React.FC = () => {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [formData, setFormData] = useState<OrderCreate>({
    customer_id: 0,
    order_type: 'صيفي',
    total_price: 0,
    payment: 0,
  });

  const queryClient = useQueryClient();

  // Queries
  const { data: orders, isLoading: ordersLoading } = useQuery('orders', fetchOrders);
  const { data: customers } = useQuery('customers', fetchCustomers);

  // Mutations
  const createMutation = useMutation(createOrder, {
    onSuccess: () => {
      queryClient.invalidateQueries('orders');
      setIsFormOpen(false);
      resetForm();
    },
  });

  const resetForm = () => {
    setFormData({
      customer_id: 0,
      order_type: 'صيفي',
      total_price: 0,
      payment: 0,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createMutation.mutate(formData);
  };

  const getCustomerName = (customerId: number) => {
    const customer = customers?.find(c => c.id === customerId);
    return customer?.name || 'غير معروف';
  };

  if (ordersLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        className="flex items-center justify-between"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-800 flex items-center">
            <ShoppingCart className="w-8 h-8 ml-3" />
            إدارة الطلبات
          </h1>
          <p className="text-gray-600 mt-1">إضافة ومتابعة طلبات العملاء</p>
        </div>
        <motion.button
          className="btn-primary flex items-center"
          onClick={() => setIsFormOpen(true)}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Plus className="w-5 h-5 ml-2" />
          إضافة طلب جديد
        </motion.button>
      </motion.div>

      {/* Orders List */}
      <motion.div
        className="space-y-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <AnimatePresence>
          {orders?.map((order, index) => (
            <motion.div
              key={order.id}
              className="card hover-lift"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="bg-secondary-100 p-3 rounded-full">
                    <Package className="w-6 h-6 text-secondary-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800 flex items-center">
                      <User className="w-4 h-4 ml-2" />
                      {getCustomerName(order.customer_id)}
                    </h3>
                    <div className="flex items-center text-gray-600 text-sm mt-1">
                      <Calendar className="w-4 h-4 ml-1" />
                      {new Date(order.created_at).toLocaleDateString('ar-SA')}
                    </div>
                  </div>
                </div>

                <div className="text-left">
                  <div className="flex items-center space-x-2 space-x-reverse mb-2">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      order.order_type === 'صيفي' 
                        ? 'bg-yellow-100 text-yellow-800' 
                        : 'bg-blue-100 text-blue-800'
                    }`}>
                      {order.order_type}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    <div className="flex items-center justify-between mb-1">
                      <span>الإجمالي:</span>
                      <span className="font-semibold">{order.total_price} د.ك</span>
                    </div>
                    <div className="flex items-center justify-between mb-1">
                      <span>المدفوع:</span>
                      <span className="font-semibold text-green-600">{order.payment} د.ك</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>المتبقي:</span>
                      <span className={`font-semibold ${
                        order.remaining > 0 ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {order.remaining} د.ك
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Order Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-xl shadow-2xl max-w-md w-full"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">إضافة طلب جديد</h2>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      العميل *
                    </label>
                    <select
                      required
                      className="input-field"
                      value={formData.customer_id}
                      onChange={(e) => setFormData({ ...formData, customer_id: parseInt(e.target.value) })}
                    >
                      <option value={0}>اختر العميل</option>
                      {customers?.map((customer) => (
                        <option key={customer.id} value={customer.id}>
                          {customer.name} - {customer.phone}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نوع الطلب *
                    </label>
                    <select
                      required
                      className="input-field"
                      value={formData.order_type}
                      onChange={(e) => setFormData({ ...formData, order_type: e.target.value })}
                    >
                      <option value="صيفي">صيفي</option>
                      <option value="شتوي">شتوي</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      السعر الإجمالي (د.ك) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      required
                      className="input-field"
                      value={formData.total_price}
                      onChange={(e) => setFormData({ ...formData, total_price: parseFloat(e.target.value) })}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      المبلغ المدفوع (د.ك) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      required
                      className="input-field"
                      value={formData.payment}
                      onChange={(e) => setFormData({ ...formData, payment: parseFloat(e.target.value) })}
                    />
                  </div>

                  {formData.total_price > 0 && formData.payment >= 0 && (
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="flex justify-between text-sm">
                        <span>المبلغ المتبقي:</span>
                        <span className={`font-semibold ${
                          (formData.total_price - formData.payment) > 0 ? 'text-red-600' : 'text-green-600'
                        }`}>
                          {(formData.total_price - formData.payment).toFixed(2)} د.ك
                        </span>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end space-x-4 space-x-reverse pt-4">
                    <motion.button
                      type="button"
                      className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                      onClick={() => {
                        setIsFormOpen(false);
                        resetForm();
                      }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      إلغاء
                    </motion.button>
                    <motion.button
                      type="submit"
                      className="btn-primary flex items-center"
                      disabled={createMutation.isLoading}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <DollarSign className="w-5 h-5 ml-2" />
                      حفظ الطلب
                    </motion.button>
                  </div>
                </form>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Orders;
