import React from 'react';
import { motion } from 'framer-motion';
import { Crown, User } from 'lucide-react';

const Header: React.FC = () => {
  return (
    <motion.header 
      className="gradient-header shadow-lg sticky top-0 z-50"
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo and Title */}
          <motion.div 
            className="flex items-center space-x-4 space-x-reverse"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <div className="bg-white bg-opacity-20 p-3 rounded-full">
              <Crown className="w-8 h-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">معاريس الجهراء</h1>
              <p className="text-primary-100 text-sm">نظام إدارة العملاء والطلبات</p>
            </div>
          </motion.div>

          {/* User Info */}
          <motion.div 
            className="flex items-center space-x-4 space-x-reverse"
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.2 }}
          >
            <div className="text-right">
              <p className="text-white font-semibold">مدير النظام</p>
              <p className="text-primary-100 text-sm">مرحباً بك</p>
            </div>
            <div className="bg-white bg-opacity-20 p-2 rounded-full">
              <User className="w-6 h-6 text-white" />
            </div>
          </motion.div>
        </div>
      </div>
    </motion.header>
  );
};

export default Header;
