<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاريس الجهراء - النسخة الحديثة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            direction: rtl;
            text-align: right;
        }
        .gradient-header {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 50%, #d68910 100%);
        }
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(243, 156, 18, 0.4);
        }
        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            transition: border-color 0.3s ease;
            text-align: right;
        }
        .input-field:focus {
            outline: none;
            border-color: #f39c12;
        }
        .stat-card {
            padding: 24px;
            border-radius: 12px;
            color: white;
            text-align: center;
        }
        .stat-card-blue { background: linear-gradient(135deg, #3498db, #2980b9); }
        .stat-card-green { background: linear-gradient(135deg, #27ae60, #229954); }
        .stat-card-orange { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .stat-card-red { background: linear-gradient(135deg, #e74c3c, #c0392b); }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #f39c12;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-header shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i data-lucide="crown" class="w-8 h-8 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-white">معاريس الجهراء</h1>
                        <p class="text-orange-100 text-sm">النسخة الحديثة - نظام إدارة العملاء والطلبات</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="text-right">
                        <p class="text-white font-semibold">مدير النظام</p>
                        <p class="text-orange-100 text-sm">مرحباً بك</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-2 rounded-full">
                        <i data-lucide="user" class="w-6 h-6 text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="bg-white shadow-md">
        <div class="container mx-auto px-6">
            <div class="flex space-x-8 space-x-reverse">
                <button onclick="showSection('dashboard')" class="nav-btn py-4 px-6 text-orange-600 border-b-2 border-orange-600 font-semibold">
                    <i data-lucide="home" class="w-5 h-5 inline ml-2"></i>
                    الرئيسية
                </button>
                <button onclick="showSection('customers')" class="nav-btn py-4 px-6 text-gray-600 hover:text-orange-600 font-semibold">
                    <i data-lucide="users" class="w-5 h-5 inline ml-2"></i>
                    العملاء
                </button>
                <button onclick="showSection('orders')" class="nav-btn py-4 px-6 text-gray-600 hover:text-orange-600 font-semibold">
                    <i data-lucide="shopping-cart" class="w-5 h-5 inline ml-2"></i>
                    الطلبات
                </button>
                <button onclick="showSection('debts')" class="nav-btn py-4 px-6 text-gray-600 hover:text-orange-600 font-semibold">
                    <i data-lucide="credit-card" class="w-5 h-5 inline ml-2"></i>
                    الديون
                </button>
                <button onclick="showSection('reports')" class="nav-btn py-4 px-6 text-gray-600 hover:text-orange-600 font-semibold">
                    <i data-lucide="bar-chart-3" class="w-5 h-5 inline ml-2"></i>
                    التقارير
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <!-- Dashboard Section -->
        <div id="dashboard" class="section">
            <div class="fade-in">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">لوحة التحكم</h2>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="stat-card stat-card-blue">
                        <i data-lucide="shopping-cart" class="w-8 h-8 mx-auto mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">طلبات اليوم</h3>
                        <p class="text-3xl font-bold" id="daily-orders">-</p>
                    </div>
                    <div class="stat-card stat-card-green">
                        <i data-lucide="dollar-sign" class="w-8 h-8 mx-auto mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">مبيعات اليوم</h3>
                        <p class="text-3xl font-bold" id="daily-sales">-</p>
                    </div>
                    <div class="stat-card stat-card-orange">
                        <i data-lucide="users" class="w-8 h-8 mx-auto mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">إجمالي العملاء</h3>
                        <p class="text-3xl font-bold" id="total-customers">-</p>
                    </div>
                    <div class="stat-card stat-card-red">
                        <i data-lucide="credit-card" class="w-8 h-8 mx-auto mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">الديون المستحقة</h3>
                        <p class="text-3xl font-bold" id="total-debts">-</p>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="card p-6 text-center cursor-pointer" onclick="showSection('customers')">
                        <i data-lucide="user-plus" class="w-12 h-12 text-orange-500 mx-auto mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">إضافة عميل جديد</h3>
                        <p class="text-gray-600">إضافة عميل جديد مع قياساته</p>
                    </div>
                    <div class="card p-6 text-center cursor-pointer" onclick="showSection('orders')">
                        <i data-lucide="plus-circle" class="w-12 h-12 text-blue-500 mx-auto mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">إنشاء طلب جديد</h3>
                        <p class="text-gray-600">إضافة طلب جديد لعميل موجود</p>
                    </div>
                    <div class="card p-6 text-center cursor-pointer" onclick="showSection('reports')">
                        <i data-lucide="trending-up" class="w-12 h-12 text-green-500 mx-auto mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">عرض التقارير</h3>
                        <p class="text-gray-600">تقارير مفصلة عن الأداء</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers Section -->
        <div id="customers" class="section hidden">
            <div class="fade-in">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-3xl font-bold text-gray-800">إدارة العملاء</h2>
                    <button onclick="showAddCustomerForm()" class="btn-primary">
                        <i data-lucide="plus" class="w-5 h-5 inline ml-2"></i>
                        إضافة عميل جديد
                    </button>
                </div>

                <!-- Search -->
                <div class="card p-4 mb-6">
                    <div class="relative">
                        <i data-lucide="search" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"></i>
                        <input type="text" id="customer-search" placeholder="البحث عن عميل بالاسم أو رقم الهاتف..."
                               class="input-field pr-10" onkeyup="searchCustomers()">
                    </div>
                </div>

                <!-- Customers List -->
                <div id="customers-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="loading mx-auto"></div>
                </div>
            </div>
        </div>

        <!-- Orders Section -->
        <div id="orders" class="section hidden">
            <div class="fade-in">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-3xl font-bold text-gray-800">إدارة الطلبات</h2>
                    <button onclick="showAddOrderForm()" class="btn-primary">
                        <i data-lucide="plus" class="w-5 h-5 inline ml-2"></i>
                        إضافة طلب جديد
                    </button>
                </div>

                <!-- Orders List -->
                <div id="orders-list" class="space-y-4">
                    <div class="loading mx-auto"></div>
                </div>
            </div>
        </div>

        <!-- Debts Section -->
        <div id="debts" class="section hidden">
            <div class="fade-in">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">إدارة الديون</h2>

                <!-- Debts List -->
                <div id="debts-list" class="space-y-4">
                    <div class="loading mx-auto"></div>
                </div>
            </div>
        </div>

        <!-- Reports Section -->
        <div id="reports" class="section hidden">
            <div class="fade-in">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">التقارير والإحصائيات</h2>

                <!-- Daily Report -->
                <div class="card p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">تقرير طلبات اليوم</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4" id="daily-report">
                        <div class="loading mx-auto"></div>
                    </div>
                </div>

                <!-- Monthly Report -->
                <div class="card p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">تقرير طلبات الشهر</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4" id="monthly-report">
                        <div class="loading mx-auto"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // API Base URL
        const API_BASE = 'http://localhost:8000/api';

        // Current section
        let currentSection = 'dashboard';

        // Show section
        function showSection(section) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(s => s.classList.add('hidden'));

            // Show selected section
            document.getElementById(section).classList.remove('hidden');

            // Update navigation
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('text-orange-600', 'border-b-2', 'border-orange-600');
                btn.classList.add('text-gray-600');
            });

            event.target.classList.remove('text-gray-600');
            event.target.classList.add('text-orange-600', 'border-b-2', 'border-orange-600');

            currentSection = section;

            // Load section data
            if (section === 'customers') loadCustomers();
            else if (section === 'orders') loadOrders();
            else if (section === 'debts') loadDebts();
            else if (section === 'reports') loadReports();
        }

        // API Functions
        async function apiGet(endpoint) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`);
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                return null;
            }
        }

        async function apiPost(endpoint, data) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                return null;
            }
        }

        // Load Dashboard Data
        async function loadDashboardData() {
            const [customers, dailyStats, monthlyStats, debts] = await Promise.all([
                apiGet('/customers'),
                apiGet('/statistics/daily'),
                apiGet('/statistics/monthly'),
                apiGet('/debts')
            ]);

            // Update dashboard stats
            document.getElementById('daily-orders').textContent = dailyStats?.total_orders || 0;
            document.getElementById('daily-sales').textContent = `${dailyStats?.total_amount || 0} د.ك`;
            document.getElementById('total-customers').textContent = customers?.length || 0;
            document.getElementById('total-debts').textContent = debts?.length || 0;
        }

        // Load Customers
        async function loadCustomers() {
            const customers = await apiGet('/customers');
            const container = document.getElementById('customers-list');

            if (!customers || customers.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-500 col-span-full">لا توجد عملاء</p>';
                return;
            }

            container.innerHTML = customers.map(customer => `
                <div class="card p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="bg-orange-100 p-2 rounded-full ml-3">
                                <i data-lucide="user" class="w-5 h-5 text-orange-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">${customer.name}</h3>
                                <p class="text-gray-600 text-sm">${customer.phone}</p>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">الصدر:</span>
                            <span class="font-medium">${customer.chest || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الكتف:</span>
                            <span class="font-medium">${customer.shoulder || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الخصر:</span>
                            <span class="font-medium">${customer.waist || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الطول:</span>
                            <span class="font-medium">${customer.height || '-'} سم</span>
                        </div>
                    </div>
                </div>
            `).join('');

            // Re-initialize icons
            lucide.createIcons();
        }

        // Load Orders
        async function loadOrders() {
            const [orders, customers] = await Promise.all([
                apiGet('/orders'),
                apiGet('/customers')
            ]);

            const container = document.getElementById('orders-list');

            if (!orders || orders.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-500">لا توجد طلبات</p>';
                return;
            }

            const getCustomerName = (customerId) => {
                const customer = customers?.find(c => c.id === customerId);
                return customer?.name || 'غير معروف';
            };

            container.innerHTML = orders.map(order => `
                <div class="card p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="bg-blue-100 p-3 rounded-full">
                                <i data-lucide="package" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">${getCustomerName(order.customer_id)}</h3>
                                <p class="text-gray-600 text-sm">${new Date(order.created_at).toLocaleDateString('ar-SA')}</p>
                            </div>
                        </div>
                        <div class="text-left">
                            <span class="px-3 py-1 rounded-full text-sm font-medium ${
                                order.order_type === 'صيفي'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-blue-100 text-blue-800'
                            }">${order.order_type}</span>
                            <div class="text-sm text-gray-600 mt-2">
                                <div>الإجمالي: <span class="font-semibold">${order.total_price} د.ك</span></div>
                                <div>المدفوع: <span class="font-semibold text-green-600">${order.payment} د.ك</span></div>
                                <div>المتبقي: <span class="font-semibold ${order.remaining > 0 ? 'text-red-600' : 'text-green-600'}">${order.remaining} د.ك</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            lucide.createIcons();
        }

        // Load Debts
        async function loadDebts() {
            const [debts, customers] = await Promise.all([
                apiGet('/debts'),
                apiGet('/customers')
            ]);

            const container = document.getElementById('debts-list');

            if (!debts || debts.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-500">لا توجد ديون</p>';
                return;
            }

            const getCustomerName = (customerId) => {
                const customer = customers?.find(c => c.id === customerId);
                return customer?.name || 'غير معروف';
            };

            container.innerHTML = debts.map(debt => `
                <div class="card p-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="bg-red-100 p-3 rounded-full">
                                <i data-lucide="credit-card" class="w-6 h-6 text-red-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">${getCustomerName(debt.customer_id)}</h3>
                                <p class="text-gray-600 text-sm">${new Date(debt.created_at).toLocaleDateString('ar-SA')}</p>
                            </div>
                        </div>
                        <div class="text-left">
                            <div class="text-sm text-gray-600">
                                <div>المبلغ الأصلي: <span class="font-semibold">${debt.amount} د.ك</span></div>
                                <div>المدفوع: <span class="font-semibold text-green-600">${debt.payment} د.ك</span></div>
                                <div>المتبقي: <span class="font-semibold text-red-600">${debt.remaining} د.ك</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            lucide.createIcons();
        }

        // Load Reports
        async function loadReports() {
            const [dailyStats, monthlyStats] = await Promise.all([
                apiGet('/statistics/daily'),
                apiGet('/statistics/monthly')
            ]);

            // Daily Report
            document.getElementById('daily-report').innerHTML = `
                <div class="bg-blue-50 p-4 rounded-lg text-center">
                    <i data-lucide="shopping-cart" class="w-8 h-8 text-blue-500 mx-auto mb-2"></i>
                    <p class="text-blue-600 text-sm font-medium">عدد الطلبات</p>
                    <p class="text-2xl font-bold text-blue-800">${dailyStats?.total_orders || 0}</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg text-center">
                    <i data-lucide="dollar-sign" class="w-8 h-8 text-green-500 mx-auto mb-2"></i>
                    <p class="text-green-600 text-sm font-medium">إجمالي المبيعات</p>
                    <p class="text-2xl font-bold text-green-800">${dailyStats?.total_amount || 0} د.ك</p>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg text-center">
                    <i data-lucide="trending-up" class="w-8 h-8 text-orange-500 mx-auto mb-2"></i>
                    <p class="text-orange-600 text-sm font-medium">المبلغ المدفوع</p>
                    <p class="text-2xl font-bold text-orange-800">${dailyStats?.paid_amount || 0} د.ك</p>
                </div>
                <div class="bg-red-50 p-4 rounded-lg text-center">
                    <i data-lucide="credit-card" class="w-8 h-8 text-red-500 mx-auto mb-2"></i>
                    <p class="text-red-600 text-sm font-medium">المبلغ المتبقي</p>
                    <p class="text-2xl font-bold text-red-800">${dailyStats?.remaining_amount || 0} د.ك</p>
                </div>
            `;

            // Monthly Report
            document.getElementById('monthly-report').innerHTML = `
                <div class="bg-purple-50 p-4 rounded-lg text-center">
                    <i data-lucide="shopping-cart" class="w-8 h-8 text-purple-500 mx-auto mb-2"></i>
                    <p class="text-purple-600 text-sm font-medium">عدد الطلبات</p>
                    <p class="text-2xl font-bold text-purple-800">${monthlyStats?.total_orders || 0}</p>
                </div>
                <div class="bg-indigo-50 p-4 rounded-lg text-center">
                    <i data-lucide="dollar-sign" class="w-8 h-8 text-indigo-500 mx-auto mb-2"></i>
                    <p class="text-indigo-600 text-sm font-medium">إجمالي المبيعات</p>
                    <p class="text-2xl font-bold text-indigo-800">${monthlyStats?.total_amount || 0} د.ك</p>
                </div>
                <div class="bg-pink-50 p-4 rounded-lg text-center">
                    <i data-lucide="trending-up" class="w-8 h-8 text-pink-500 mx-auto mb-2"></i>
                    <p class="text-pink-600 text-sm font-medium">المبلغ المدفوع</p>
                    <p class="text-2xl font-bold text-pink-800">${monthlyStats?.paid_amount || 0} د.ك</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg text-center">
                    <i data-lucide="credit-card" class="w-8 h-8 text-yellow-500 mx-auto mb-2"></i>
                    <p class="text-yellow-600 text-sm font-medium">المبلغ المتبقي</p>
                    <p class="text-2xl font-bold text-yellow-800">${monthlyStats?.remaining_amount || 0} د.ك</p>
                </div>
            `;

            lucide.createIcons();
        }

        // Search Customers
        async function searchCustomers() {
            const query = document.getElementById('customer-search').value;
            if (query.length < 2) {
                loadCustomers();
                return;
            }

            const customers = await apiGet(`/customers/search/${encodeURIComponent(query)}`);
            const container = document.getElementById('customers-list');

            if (!customers || customers.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-500 col-span-full">لا توجد نتائج</p>';
                return;
            }

            container.innerHTML = customers.map(customer => `
                <div class="card p-6">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="bg-orange-100 p-2 rounded-full ml-3">
                                <i data-lucide="user" class="w-5 h-5 text-orange-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">${customer.name}</h3>
                                <p class="text-gray-600 text-sm">${customer.phone}</p>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">الصدر:</span>
                            <span class="font-medium">${customer.chest || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الكتف:</span>
                            <span class="font-medium">${customer.shoulder || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الخصر:</span>
                            <span class="font-medium">${customer.waist || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الطول:</span>
                            <span class="font-medium">${customer.height || '-'} سم</span>
                        </div>
                    </div>
                </div>
            `).join('');

            lucide.createIcons();
        }

        // Placeholder functions for forms
        function showAddCustomerForm() {
            alert('نموذج إضافة العميل سيتم تطويره قريباً');
        }

        function showAddOrderForm() {
            alert('نموذج إضافة الطلب سيتم تطويره قريباً');
        }
    </script>
    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Load initial data
        loadDashboardData();
    </script>
</body>
</html>
