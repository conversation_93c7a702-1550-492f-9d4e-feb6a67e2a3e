using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading;

namespace MaarisAlJahra.Helpers
{
    public static class LanguageManager
    {
        public enum Language
        {
            Arabic,
            English,
            French
        }

        private static Language currentLanguage = Language.Arabic;
        private static Dictionary<string, Dictionary<Language, string>> translations;

        static LanguageManager()
        {
            InitializeTranslations();
        }

        public static Language CurrentLanguage
        {
            get => currentLanguage;
            set
            {
                currentLanguage = value;
                SetCulture();
            }
        }

        private static void SetCulture()
        {
            CultureInfo culture = currentLanguage switch
            {
                Language.Arabic => new CultureInfo("ar-SA"),
                Language.English => new CultureInfo("en-US"),
                Language.French => new CultureInfo("fr-FR"),
                _ => new CultureInfo("ar-SA")
            };

            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
        }

        public static string GetText(string key)
        {
            if (translations.ContainsKey(key) && translations[key].ContainsKey(currentLanguage))
            {
                return translations[key][currentLanguage];
            }
            return key; // Return key if translation not found
        }

        private static void InitializeTranslations()
        {
            translations = new Dictionary<string, Dictionary<Language, string>>
            {
                // Application Title
                ["AppTitle"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "معاريس الجهراء",
                    [Language.English] = "Ma'aris Al Jahra",
                    [Language.French] = "Ma'aris Al Jahra"
                },

                // Main Menu
                ["SystemManager"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "مدير النظام",
                    [Language.English] = "System Manager",
                    [Language.French] = "Gestionnaire Système"
                },
                ["Employee"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "موظف",
                    [Language.English] = "Employee",
                    [Language.French] = "Employé"
                },

                // Management Sections
                ["WorkersManagement"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "إدارة العمال",
                    [Language.English] = "Workers Management",
                    [Language.French] = "Gestion des Employés"
                },
                ["OrdersManagement"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "إدارة الطلبات",
                    [Language.English] = "Orders Management",
                    [Language.French] = "Gestion des Commandes"
                },
                ["DebtsManagement"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "إدارة الديون",
                    [Language.English] = "Debts Management",
                    [Language.French] = "Gestion des Dettes"
                },

                // Worker Form
                ["WorkerName"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "اسم العامل",
                    [Language.English] = "Worker Name",
                    [Language.French] = "Nom de l'Employé"
                },
                ["Phone"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "رقم الهاتف",
                    [Language.English] = "Phone Number",
                    [Language.French] = "Numéro de Téléphone"
                },
                ["Position"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "المنصب",
                    [Language.English] = "Position",
                    [Language.French] = "Poste"
                },
                ["Salary"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "الراتب",
                    [Language.English] = "Salary",
                    [Language.French] = "Salaire"
                },

                // Order Form
                ["CustomerName"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "اسم العميل",
                    [Language.English] = "Customer Name",
                    [Language.French] = "Nom du Client"
                },
                ["OrderType"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "نوع الطلب",
                    [Language.English] = "Order Type",
                    [Language.French] = "Type de Commande"
                },
                ["TotalPrice"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "السعر الإجمالي",
                    [Language.English] = "Total Price",
                    [Language.French] = "Prix Total"
                },
                ["Payment"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "المبلغ المدفوع",
                    [Language.English] = "Payment",
                    [Language.French] = "Paiement"
                },

                // Buttons
                ["Add"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "إضافة",
                    [Language.English] = "Add",
                    [Language.French] = "Ajouter"
                },
                ["Update"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "تحديث",
                    [Language.English] = "Update",
                    [Language.French] = "Mettre à jour"
                },
                ["Delete"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "حذف",
                    [Language.English] = "Delete",
                    [Language.French] = "Supprimer"
                },
                ["Back"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "رجوع",
                    [Language.English] = "Back",
                    [Language.French] = "Retour"
                },

                // Messages
                ["Loading"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "جاري التحميل...",
                    [Language.English] = "Loading...",
                    [Language.French] = "Chargement..."
                },
                ["Welcome"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "مرحباً بك في معاريس الجهراء",
                    [Language.English] = "Welcome to Ma'aris Al Jahra",
                    [Language.French] = "Bienvenue à Ma'aris Al Jahra"
                },
                ["EmployeeFeatureInDevelopment"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "وظيفة الموظف قيد التطوير",
                    [Language.English] = "Employee feature is under development",
                    [Language.French] = "La fonction employé est en développement"
                },

                // Error Messages
                ["Error"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "خطأ",
                    [Language.English] = "Error",
                    [Language.French] = "Erreur"
                },
                ["InputError"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "خطأ في الإدخال",
                    [Language.English] = "Input Error",
                    [Language.French] = "Erreur de Saisie"
                },
                ["PleaseEnterWorkerName"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "يرجى إدخال اسم العامل",
                    [Language.English] = "Please enter worker name",
                    [Language.French] = "Veuillez saisir le nom de l'employé"
                },
                ["PleaseEnterValidSalary"] = new Dictionary<Language, string>
                {
                    [Language.Arabic] = "يرجى إدخال راتب صحيح",
                    [Language.English] = "Please enter a valid salary",
                    [Language.French] = "Veuillez saisir un salaire valide"
                }
            };
        }

        public static bool IsRightToLeft()
        {
            return currentLanguage == Language.Arabic;
        }

        public static string[] GetAvailableLanguages()
        {
            return new string[] { "العربية", "English", "Français" };
        }

        public static void SetLanguageByIndex(int index)
        {
            CurrentLanguage = index switch
            {
                0 => Language.Arabic,
                1 => Language.English,
                2 => Language.French,
                _ => Language.Arabic
            };
        }
    }
}
