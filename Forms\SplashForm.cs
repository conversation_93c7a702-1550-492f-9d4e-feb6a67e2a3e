using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using MaarisAlJahra.Helpers;
using Guna.UI2.WinForms;

namespace MaarisAlJahra.Forms
{
    public partial class SplashForm : Form
    {
        private Guna2CircleProgressBar progressBar;
        private Label titleLabel;
        private System.Windows.Forms.Timer autoCloseTimer;

        public SplashForm()
        {
            InitializeComponent();
            SetupForm();
            SetupControls();
            StartAnimation();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 600);
            this.FormBorderStyle = FormBorderStyle.None;
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Name = "SplashForm";
            this.Text = "معاريس الجهراء";

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer, true);

            StyleHelper.ApplyGradientBackground(this);
        }

        private void SetupControls()
        {
            // Title Label
            titleLabel = new Label
            {
                Text = "معاريس الجهراء",
                Font = new Font("Segoe UI", 48, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Center the title
            titleLabel.Location = new Point(
                (this.Width - titleLabel.Width) / 2,
                (this.Height - titleLabel.Height) / 2 - 100
            );

            // Apply text shadow effect
            StyleHelper.ApplyTextShadow(titleLabel, titleLabel.Text, titleLabel.Font,
                Color.White, Color.FromArgb(100, 0, 0, 0), new Point(3, 3));

            // Progress Bar
            progressBar = new Guna2CircleProgressBar
            {
                Size = new Size(100, 100),
                ProgressColor = Color.White,
                ProgressColor2 = StyleHelper.AccentColor,
                InnerColor = Color.Transparent,
                ProgressThickness = 8,
                Value = 0,
                Minimum = 0,
                Maximum = 100,
                AnimationSpeed = 2.0f
            };

            // Center the progress bar
            progressBar.Location = new Point(
                (this.Width - progressBar.Width) / 2,
                titleLabel.Bottom + 50
            );

            this.Controls.Add(titleLabel);
            this.Controls.Add(progressBar);

            // Handle resize to keep controls centered
            this.Resize += (s, e) =>
            {
                titleLabel.Location = new Point(
                    (this.Width - titleLabel.Width) / 2,
                    (this.Height - titleLabel.Height) / 2 - 100
                );

                progressBar.Location = new Point(
                    (this.Width - progressBar.Width) / 2,
                    titleLabel.Bottom + 50
                );
            };
        }

        private async void StartAnimation()
        {
            // Fade in effect
            await AnimationHelper.FadeIn(this, 800);

            // Start progress animation
            var progressTimer = new System.Windows.Forms.Timer { Interval = 30 };
            progressTimer.Tick += (s, e) =>
            {
                if (progressBar.Value < 100)
                {
                    progressBar.Value += 2;
                }
                else
                {
                    progressTimer.Stop();
                    progressTimer.Dispose();
                }
            };
            progressTimer.Start();

            // Auto close timer
            autoCloseTimer = new System.Windows.Forms.Timer { Interval = 3000 };
            autoCloseTimer.Tick += async (s, e) =>
            {
                autoCloseTimer.Stop();
                autoCloseTimer.Dispose();

                await AnimationHelper.FadeOut(this, 500);

                var loginForm = new LoginForm();
                loginForm.Show();
                this.Hide();
            };
            autoCloseTimer.Start();
        }

        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);

            // Additional gradient overlay for better visual effect
            using (var brush = new LinearGradientBrush(
                this.ClientRectangle,
                Color.FromArgb(50, 0, 0, 0),
                Color.FromArgb(100, 0, 0, 0),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, this.ClientRectangle);
            }
        }

        protected override void Dispose(bool disposing)
        {
            autoCloseTimer?.Dispose();
            base.Dispose(disposing);
        }
    }
}
