using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Microsoft.EntityFrameworkCore;
using MaarisAlJahra.Helpers;
using MaarisAlJahra.Models;

namespace MaarisAlJahra.Forms
{
    public partial class WorkersForm : Form
    {
        private DataGridView workersGrid;
        private TextBox nameTextBox;
        private TextBox phoneTextBox;
        private TextBox positionTextBox;
        private TextBox salaryTextBox;
        private Button addButton;
        private Button updateButton;
        private Button deleteButton;
        private Label titleLabel;
        private int selectedWorkerId = -1;

        public WorkersForm()
        {
            InitializeComponent();
            SetupForm();
            SetupControls();
            LoadWorkers();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.WindowState = FormWindowState.Normal;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.Name = "WorkersForm";
            this.Text = "إدارة العمال";

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer, true);

            StyleHelper.ApplyGradientBackground(this);
        }

        private void SetupControls()
        {
            // Title
            titleLabel = new Label
            {
                Text = "إدارة العمال",
                Font = new Font("Segoe UI", 28, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                AutoSize = true,
                Location = new Point(50, 30)
            };

            // Input Controls
            CreateInputControls();

            // Action Buttons
            CreateActionButtons();

            // Data Grid
            CreateDataGrid();

            this.Controls.Add(titleLabel);
            this.Controls.Add(nameTextBox);
            this.Controls.Add(phoneTextBox);
            this.Controls.Add(positionTextBox);
            this.Controls.Add(salaryTextBox);
            this.Controls.Add(addButton);
            this.Controls.Add(updateButton);
            this.Controls.Add(deleteButton);
            this.Controls.Add(workersGrid);
        }

        private void CreateInputControls()
        {
            var startY = 120;
            var spacing = 80;

            nameTextBox = CreateStyledTextBox("اسم العامل", new Point(50, startY));
            phoneTextBox = CreateStyledTextBox("رقم الهاتف", new Point(50, startY + spacing));
            positionTextBox = CreateStyledTextBox("المنصب", new Point(50, startY + spacing * 2));
            salaryTextBox = CreateStyledTextBox("الراتب", new Point(50, startY + spacing * 3));
        }

        private TextBox CreateStyledTextBox(string placeholder, Point location)
        {
            var textBox = new TextBox
            {
                Size = new Size(300, 30),
                Location = location,
                Font = new Font("Segoe UI", 12),
                BackColor = Color.White,
                ForeColor = Color.Black,
                BorderStyle = BorderStyle.FixedSingle
            };

            // Add placeholder functionality
            textBox.Text = placeholder;
            textBox.ForeColor = Color.Gray;

            textBox.Enter += (s, e) =>
            {
                if (textBox.Text == placeholder)
                {
                    textBox.Text = "";
                    textBox.ForeColor = Color.Black;
                }
            };

            textBox.Leave += (s, e) =>
            {
                if (string.IsNullOrWhiteSpace(textBox.Text))
                {
                    textBox.Text = placeholder;
                    textBox.ForeColor = Color.Gray;
                }
            };

            return textBox;
        }

        private void CreateActionButtons()
        {
            var buttonY = 450;
            var buttonSpacing = 120;

            addButton = StyleHelper.CreateStyledButton("إضافة", StyleHelper.SuccessColor,
                new Size(100, 40), AddWorker_Click);
            addButton.Location = new Point(50, buttonY);

            updateButton = StyleHelper.CreateStyledButton("تحديث", StyleHelper.AccentColor,
                new Size(100, 40), UpdateWorker_Click);
            updateButton.Location = new Point(50 + buttonSpacing, buttonY);

            deleteButton = StyleHelper.CreateStyledButton("حذف", StyleHelper.DangerColor,
                new Size(100, 40), DeleteWorker_Click);
            deleteButton.Location = new Point(50 + buttonSpacing * 2, buttonY);
        }

        private void CreateDataGrid()
        {
            workersGrid = new DataGridView
            {
                Location = new Point(400, 120),
                Size = new Size(750, 600),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            StyleHelper.StyleDataGridView(workersGrid);

            workersGrid.SelectionChanged += (s, e) =>
            {
                if (workersGrid.SelectedRows.Count > 0)
                {
                    var row = workersGrid.SelectedRows[0];
                    selectedWorkerId = (int)row.Cells["Id"].Value;
                    nameTextBox.Text = row.Cells["Name"].Value?.ToString() ?? "";
                    phoneTextBox.Text = row.Cells["Phone"].Value?.ToString() ?? "";
                    positionTextBox.Text = row.Cells["Position"].Value?.ToString() ?? "";
                    salaryTextBox.Text = row.Cells["Salary"].Value?.ToString() ?? "";
                }
            };
        }

        private async void LoadWorkers()
        {
            try
            {
                using var context = new DatabaseContext();
                var workers = await context.Workers.Where(w => w.IsActive).ToListAsync();

                workersGrid.DataSource = workers.Select(w => new
                {
                    w.Id,
                    w.Name,
                    w.Phone,
                    w.Position,
                    w.Salary,
                    HireDate = w.HireDate.ToString("yyyy-MM-dd")
                }).ToList();

                workersGrid.Columns["Id"].Visible = false;
                workersGrid.Columns["Name"].HeaderText = "الاسم";
                workersGrid.Columns["Phone"].HeaderText = "الهاتف";
                workersGrid.Columns["Position"].HeaderText = "المنصب";
                workersGrid.Columns["Salary"].HeaderText = "الراتب";
                workersGrid.Columns["HireDate"].HeaderText = "تاريخ التوظيف";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void AddWorker_Click(object sender, EventArgs e)
        {
            try
            {
                if (!ValidateInput()) return;

                // Removed animation for better performance

                using var context = new DatabaseContext();
                var worker = new Worker
                {
                    Name = (nameTextBox.Text == "اسم العامل" ? "" : nameTextBox.Text).Trim(),
                    Phone = (phoneTextBox.Text == "رقم الهاتف" ? "" : phoneTextBox.Text).Trim(),
                    Position = (positionTextBox.Text == "المنصب" ? "" : positionTextBox.Text).Trim(),
                    Salary = decimal.Parse((salaryTextBox.Text == "الراتب" ? "" : salaryTextBox.Text).Trim()),
                    HireDate = DateTime.Now,
                    IsActive = true
                };

                context.Workers.Add(worker);
                await context.SaveChangesAsync();

                ClearInputs();
                LoadWorkers();

                MessageBox.Show("تم إضافة العامل بنجاح", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة العامل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void UpdateWorker_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedWorkerId == -1)
                {
                    MessageBox.Show("يرجى اختيار عامل للتحديث", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!ValidateInput()) return;

                // Removed animation for better performance

                using var context = new DatabaseContext();
                var worker = await context.Workers.FindAsync(selectedWorkerId);

                if (worker != null)
                {
                    worker.Name = (nameTextBox.Text == "اسم العامل" ? "" : nameTextBox.Text).Trim();
                    worker.Phone = (phoneTextBox.Text == "رقم الهاتف" ? "" : phoneTextBox.Text).Trim();
                    worker.Position = (positionTextBox.Text == "المنصب" ? "" : positionTextBox.Text).Trim();
                    worker.Salary = decimal.Parse((salaryTextBox.Text == "الراتب" ? "" : salaryTextBox.Text).Trim());

                    await context.SaveChangesAsync();

                    ClearInputs();
                    LoadWorkers();

                    MessageBox.Show("تم تحديث العامل بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث العامل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void DeleteWorker_Click(object sender, EventArgs e)
        {
            try
            {
                if (selectedWorkerId == -1)
                {
                    MessageBox.Show("يرجى اختيار عامل للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف هذا العامل؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Removed animation for better performance

                    using var context = new DatabaseContext();
                    var worker = await context.Workers.FindAsync(selectedWorkerId);

                    if (worker != null)
                    {
                        worker.IsActive = false; // Soft delete
                        await context.SaveChangesAsync();

                        ClearInputs();
                        LoadWorkers();

                        MessageBox.Show("تم حذف العامل بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العامل: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool ValidateInput()
        {
            var nameText = nameTextBox.Text == "اسم العامل" ? "" : nameTextBox.Text;
            var salaryText = salaryTextBox.Text == "الراتب" ? "" : salaryTextBox.Text;

            if (string.IsNullOrWhiteSpace(nameText))
            {
                MessageBox.Show("يرجى إدخال اسم العامل", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(salaryText) ||
                !decimal.TryParse(salaryText, out _))
            {
                MessageBox.Show("يرجى إدخال راتب صحيح", "خطأ في الإدخال",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                salaryTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearInputs()
        {
            nameTextBox.Text = "اسم العامل";
            nameTextBox.ForeColor = Color.Gray;
            phoneTextBox.Text = "رقم الهاتف";
            phoneTextBox.ForeColor = Color.Gray;
            positionTextBox.Text = "المنصب";
            positionTextBox.ForeColor = Color.Gray;
            salaryTextBox.Text = "الراتب";
            salaryTextBox.ForeColor = Color.Gray;
            selectedWorkerId = -1;
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                this.Close();
            }
            base.OnKeyDown(e);
        }
    }
}