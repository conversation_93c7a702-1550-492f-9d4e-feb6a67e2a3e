import React from 'react';
import { motion } from 'framer-motion';
import { useQuery } from 'react-query';
import { CreditCard, User, Calendar, DollarSign } from 'lucide-react';
import { fetchDebts, fetchCustomers } from '../services/api';

const Debts: React.FC = () => {
  const { data: debts, isLoading } = useQuery('debts', fetchDebts);
  const { data: customers } = useQuery('customers', fetchCustomers);

  const getCustomerName = (customerId: number) => {
    const customer = customers?.find(c => c.id === customerId);
    return customer?.name || 'غير معروف';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1 className="text-3xl font-bold text-gray-800 flex items-center">
          <CreditCard className="w-8 h-8 ml-3" />
          إدارة الديون
        </h1>
        <p className="text-gray-600 mt-1">متابعة ديون العملاء والمبالغ المستحقة</p>
      </motion.div>

      {/* Debts List */}
      <motion.div
        className="space-y-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        {debts?.map((debt, index) => (
          <motion.div
            key={debt.id}
            className="card hover-lift"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.05 }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="bg-red-100 p-3 rounded-full">
                  <CreditCard className="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800 flex items-center">
                    <User className="w-4 h-4 ml-2" />
                    {getCustomerName(debt.customer_id)}
                  </h3>
                  <div className="flex items-center text-gray-600 text-sm mt-1">
                    <Calendar className="w-4 h-4 ml-1" />
                    {new Date(debt.created_at).toLocaleDateString('ar-SA')}
                  </div>
                </div>
              </div>

              <div className="text-left">
                <div className="text-sm text-gray-600">
                  <div className="flex items-center justify-between mb-1">
                    <span>المبلغ الأصلي:</span>
                    <span className="font-semibold">{debt.amount} د.ك</span>
                  </div>
                  <div className="flex items-center justify-between mb-1">
                    <span>المدفوع:</span>
                    <span className="font-semibold text-green-600">{debt.payment} د.ك</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>المتبقي:</span>
                    <span className="font-semibold text-red-600">{debt.remaining} د.ك</span>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>

      {debts?.length === 0 && (
        <motion.div
          className="text-center py-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-600 mb-2">لا توجد ديون</h3>
          <p className="text-gray-500">جميع العملاء قد سددوا مستحقاتهم</p>
        </motion.div>
      )}
    </div>
  );
};

export default Debts;
