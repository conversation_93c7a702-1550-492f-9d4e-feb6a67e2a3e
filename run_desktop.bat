@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    معاريس الجهراء - تطبيق سطح المكتب
echo    Ma'aris Al Jahra - Desktop App
echo ========================================
echo.

echo 🔍 فحص متطلبات النظام...
echo 🔍 Checking system requirements...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo ❌ Python is not installed
    echo يرجى تثبيت Python من: https://python.org
    echo Please install Python from: https://python.org
    pause
    exit /b 1
)

echo ✅ Python مثبت
echo ✅ Python is installed

echo.
echo 📦 تثبيت المتطلبات...
echo 📦 Installing requirements...

REM Install PySide6
echo تثبيت PySide6...
echo Installing PySide6...
pip install PySide6 --quiet

if errorlevel 1 (
    echo ❌ فشل في تثبيت PySide6
    echo ❌ Failed to install PySide6
    echo جاري المحاولة مع pip3...
    echo Trying with pip3...
    pip3 install PySide6 --quiet
)

echo ✅ تم تثبيت المتطلبات
echo ✅ Requirements installed

echo.
echo 🚀 تشغيل التطبيق...
echo 🚀 Starting application...
echo.

REM Run the desktop application
python main_desktop.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo ❌ Error running application
    echo.
    echo جاري المحاولة مع python3...
    echo Trying with python3...
    python3 main_desktop.py
)

echo.
echo 👋 شكراً لاستخدام معاريس الجهراء
echo 👋 Thank you for using Ma'aris Al Jahra
pause
