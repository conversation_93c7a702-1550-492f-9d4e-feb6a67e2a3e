import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { 
  Users, 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Save,
  X,
  Phone,
  User
} from 'lucide-react';
import { 
  fetchCustomers, 
  createCustomer, 
  updateCustomer, 
  deleteCustomer,
  searchCustomers,
  Customer, 
  CustomerCreate 
} from '../services/api';

const Customers: React.FC = () => {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [formData, setFormData] = useState<CustomerCreate>({
    name: '',
    phone: '',
    chest: undefined,
    shoulder: undefined,
    hand: undefined,
    waist: undefined,
    hip: undefined,
    inseam: undefined,
    neck: undefined,
    height: undefined,
  });

  const queryClient = useQueryClient();

  // Queries
  const { data: customers, isLoading } = useQuery(
    ['customers', searchQuery],
    () => searchQuery ? searchCustomers(searchQuery) : fetchCustomers(),
    { enabled: true }
  );

  // Mutations
  const createMutation = useMutation(createCustomer, {
    onSuccess: () => {
      queryClient.invalidateQueries('customers');
      setIsFormOpen(false);
      resetForm();
    },
  });

  const updateMutation = useMutation(
    ({ id, data }: { id: number; data: CustomerCreate }) => updateCustomer(id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('customers');
        setIsFormOpen(false);
        setEditingCustomer(null);
        resetForm();
      },
    }
  );

  const deleteMutation = useMutation(deleteCustomer, {
    onSuccess: () => {
      queryClient.invalidateQueries('customers');
    },
  });

  const resetForm = () => {
    setFormData({
      name: '',
      phone: '',
      chest: undefined,
      shoulder: undefined,
      hand: undefined,
      waist: undefined,
      hip: undefined,
      inseam: undefined,
      neck: undefined,
      height: undefined,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingCustomer) {
      updateMutation.mutate({ id: editingCustomer.id, data: formData });
    } else {
      createMutation.mutate(formData);
    }
  };

  const handleEdit = (customer: Customer) => {
    setEditingCustomer(customer);
    setFormData({
      name: customer.name,
      phone: customer.phone,
      chest: customer.chest,
      shoulder: customer.shoulder,
      hand: customer.hand,
      waist: customer.waist,
      hip: customer.hip,
      inseam: customer.inseam,
      neck: customer.neck,
      height: customer.height,
    });
    setIsFormOpen(true);
  };

  const handleDelete = (id: number) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      deleteMutation.mutate(id);
    }
  };

  const measurementFields = [
    { key: 'chest', label: 'الصدر' },
    { key: 'shoulder', label: 'الكتف' },
    { key: 'hand', label: 'اليد' },
    { key: 'waist', label: 'الخصر' },
    { key: 'hip', label: 'الورك' },
    { key: 'inseam', label: 'طول الساق' },
    { key: 'neck', label: 'الرقبة' },
    { key: 'height', label: 'الطول' },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="spinner"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        className="flex items-center justify-between"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-800 flex items-center">
            <Users className="w-8 h-8 ml-3" />
            إدارة العملاء
          </h1>
          <p className="text-gray-600 mt-1">إضافة وتعديل بيانات العملاء وقياساتهم</p>
        </div>
        <motion.button
          className="btn-primary flex items-center"
          onClick={() => {
            setEditingCustomer(null);
            resetForm();
            setIsFormOpen(true);
          }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Plus className="w-5 h-5 ml-2" />
          إضافة عميل جديد
        </motion.button>
      </motion.div>

      {/* Search */}
      <motion.div
        className="card"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <div className="relative">
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="البحث عن عميل بالاسم أو رقم الهاتف..."
            className="input-field pr-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </motion.div>

      {/* Customers Grid */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <AnimatePresence>
          {customers?.map((customer, index) => (
            <motion.div
              key={customer.id}
              className="card hover-lift"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              whileHover={{ y: -5 }}
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="bg-primary-100 p-2 rounded-full ml-3">
                    <User className="w-5 h-5 text-primary-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-800">{customer.name}</h3>
                    <div className="flex items-center text-gray-600 text-sm mt-1">
                      <Phone className="w-4 h-4 ml-1" />
                      {customer.phone}
                    </div>
                  </div>
                </div>
                <div className="flex space-x-2 space-x-reverse">
                  <motion.button
                    className="p-2 text-secondary-600 hover:bg-secondary-50 rounded-lg transition-colors"
                    onClick={() => handleEdit(customer)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Edit className="w-4 h-4" />
                  </motion.button>
                  <motion.button
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    onClick={() => handleDelete(customer.id)}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <Trash2 className="w-4 h-4" />
                  </motion.button>
                </div>
              </div>

              {/* Measurements Preview */}
              <div className="grid grid-cols-2 gap-2 text-sm">
                {measurementFields.slice(0, 4).map((field) => {
                  const value = customer[field.key as keyof Customer];
                  return (
                    <div key={field.key} className="flex justify-between">
                      <span className="text-gray-600">{field.label}:</span>
                      <span className="font-medium">
                        {value ? `${value} سم` : '-'}
                      </span>
                    </div>
                  );
                })}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Customer Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-800">
                    {editingCustomer ? 'تعديل العميل' : 'إضافة عميل جديد'}
                  </h2>
                  <motion.button
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                    onClick={() => {
                      setIsFormOpen(false);
                      setEditingCustomer(null);
                      resetForm();
                    }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <X className="w-6 h-6" />
                  </motion.button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Basic Info */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم العميل *
                      </label>
                      <input
                        type="text"
                        required
                        className="input-field"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        رقم الهاتف *
                      </label>
                      <input
                        type="tel"
                        required
                        className="input-field"
                        value={formData.phone}
                        onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                      />
                    </div>
                  </div>

                  {/* Measurements */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">القياسات</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {measurementFields.map((field) => (
                        <div key={field.key}>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {field.label} (سم)
                          </label>
                          <input
                            type="number"
                            step="0.1"
                            className="input-field"
                            value={formData[field.key as keyof CustomerCreate] || ''}
                            onChange={(e) => setFormData({ 
                              ...formData, 
                              [field.key]: e.target.value ? parseFloat(e.target.value) : undefined 
                            })}
                          />
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Form Actions */}
                  <div className="flex justify-end space-x-4 space-x-reverse pt-6 border-t">
                    <motion.button
                      type="button"
                      className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                      onClick={() => {
                        setIsFormOpen(false);
                        setEditingCustomer(null);
                        resetForm();
                      }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      إلغاء
                    </motion.button>
                    <motion.button
                      type="submit"
                      className="btn-primary flex items-center"
                      disabled={createMutation.isLoading || updateMutation.isLoading}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Save className="w-5 h-5 ml-2" />
                      {editingCustomer ? 'تحديث' : 'حفظ'}
                    </motion.button>
                  </div>
                </form>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Customers;
