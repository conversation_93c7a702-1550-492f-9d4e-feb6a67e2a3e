{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {}, ".NETCoreApp,Version=v6.0/win-x64": {"MaarisAlJahra/1.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite": "7.0.0", "Microsoft.EntityFrameworkCore.Tools": "7.0.0", "Microsoft.NET.ILLink.Analyzers": "7.0.100-1.23211.1", "Microsoft.NET.ILLink.Tasks": "7.0.100-1.23211.1", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "6.0.36", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "6.0.36"}, "runtime": {"MaarisAlJahra.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/6.0.36": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.100.3624.51421"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "netstandard.dll": {"assemblyVersion": "2.1.0.0", "fileVersion": "6.0.3624.51421"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.40.33810.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "42.42.42.42424"}, "api-ms-win-core-console-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-console-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-datetime-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-debug-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-errorhandling-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-fibers-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-file-l2-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-handle-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-heap-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-interlocked-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-libraryloader-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-localization-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-memory-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-namedpipe-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processenvironment-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processthreads-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-processthreads-l1-1-1.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-profile-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-rtlsupport-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-string-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-synch-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-synch-l1-2-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-sysinfo-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-timezone-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-core-util-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-conio-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-convert-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-environment-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-filesystem-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-heap-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-locale-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-math-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-multibyte-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-private-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-process-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-runtime-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-stdio-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-string-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-time-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "api-ms-win-crt-utility-l1-1-0.dll": {"fileVersion": "10.0.22000.194"}, "clretwrc.dll": {"fileVersion": "6.0.3624.51421"}, "clrjit.dll": {"fileVersion": "6.0.3624.51421"}, "coreclr.dll": {"fileVersion": "6.0.3624.51421"}, "createdump.exe": {"fileVersion": "6.0.3624.51421"}, "dbgshim.dll": {"fileVersion": "6.0.3624.51421"}, "hostfxr.dll": {"fileVersion": "6.0.3624.51421"}, "hostpolicy.dll": {"fileVersion": "6.0.3624.51421"}, "mscordaccore.dll": {"fileVersion": "6.0.3624.51421"}, "mscordaccore_amd64_amd64_6.0.3624.51421.dll": {"fileVersion": "6.0.3624.51421"}, "mscordbi.dll": {"fileVersion": "6.0.3624.51421"}, "mscorrc.dll": {"fileVersion": "6.0.3624.51421"}, "msquic.dll": {"fileVersion": "*******"}, "ucrtbase.dll": {"fileVersion": "10.0.22000.194"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/6.0.36": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "DirectWriteForwarder.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3624.51513"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "PresentationCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework-SystemCore.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework-SystemData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework-SystemDrawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework-SystemXml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework-SystemXmlLinq.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.Aero.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.Aero2.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.AeroLite.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.Classic.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.Luna.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.Royale.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationFramework.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "PresentationUI.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "ReachFramework.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Printing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Windows.Controls.Ribbon.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51513"}, "System.Windows.Input.Manipulations.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "System.Windows.Presentation.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "System.Xaml.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "UIAutomationClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "UIAutomationClientSideProviders.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "UIAutomationProvider.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "UIAutomationTypes.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}, "WindowsFormsIntegration.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51603"}}, "native": {"D3DCompiler_47_cor3.dll": {"fileVersion": "10.0.22621.3233"}, "PenImc_cor3.dll": {"fileVersion": "6.0.3624.51603"}, "PresentationNative_cor3.dll": {"fileVersion": "6.0.24.46601"}, "vcruntime140_cor3.dll": {"fileVersion": "14.40.33810.0"}, "wpfgfx_cor3.dll": {"fileVersion": "6.0.3624.51603"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Microsoft.Data.Sqlite.Core/7.0.0": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "7.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "7.0.0", "Microsoft.Extensions.Caching.Memory": "7.0.0", "Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.Logging": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.0": {}, "Microsoft.EntityFrameworkCore.Design/7.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.EntityFrameworkCore.Relational": "7.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "7.0.0", "Microsoft.Extensions.Configuration.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Sqlite/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "7.0.0", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.2"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/7.0.0": {"dependencies": {"Microsoft.Data.Sqlite.Core": "7.0.0", "Microsoft.EntityFrameworkCore.Relational": "7.0.0", "Microsoft.Extensions.DependencyModel": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51807"}}}, "Microsoft.EntityFrameworkCore.Tools/7.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "7.0.0"}}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.DependencyModel/7.0.0": {"dependencies": {"System.Text.Encodings.Web": "7.0.0", "System.Text.Json": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "7.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Logging.Abstractions": "7.0.0", "Microsoft.Extensions.Options": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Options/7.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0", "Microsoft.Extensions.Primitives": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Extensions.Primitives/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.1.1"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.2", "SQLitePCLRaw.provider.e_sqlite3": "2.1.2"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.core/2.1.2": {"dependencies": {"System.Memory": "4.5.3"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"native": {"runtimes/win-x64/native/e_sqlite3.dll": {"fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0-windows7.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "System.CodeDom/4.4.0": {}, "System.Memory/4.5.3": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Text.Encodings.Web/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net6.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}, "System.Text.Json/7.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "7.0.0"}, "runtime": {"lib/net6.0/System.Text.Json.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}}}}, "libraries": {"MaarisAlJahra/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/6.0.36": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/6.0.36": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WC7SANtaFTmQ/WPyhrE96h88MrH28C7kTBenDf5Eo+IR6CbWM0Uw2pR2++tyYr3qe/zSIsIYroEupB1mLg/adw==", "path": "microsoft.data.sqlite.core/7.0.0", "hashPath": "microsoft.data.sqlite.core.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9W+IfmAzMrp2ZpKZLhgTlWljSBM9Erldis1us61DAGi+L7Q6vilTbe1G2zDxtYO8F2H0I0Qnupdx5Cp4s2xoZw==", "path": "microsoft.entityframeworkcore/7.0.0", "hashPath": "microsoft.entityframeworkcore.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Pfu3Zjj5+d2Gt27oE9dpGiF/VobBB+s5ogrfI9sBsXQE1SG49RqVz5+IyeNnzhyejFrPIQsPDRMchhcojy4Hbw==", "path": "microsoft.entityframeworkcore.abstractions/7.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qkd2H+jLe37o5ku+LjT6qf7kAHY75Yfn2bBDQgqr13DTOLYpEy1Mt93KPFjaZvIu/srEcbfGGMRL7urKm5zN8Q==", "path": "microsoft.entityframeworkcore.analyzers/7.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fEEU/zZ/VblZRQxHNZxgGKVtEtOGqEAmuHkACV1i0H031bM8PQKTS7PlKPVOgg0C1v+6yeHoIAGGgbAvG9f7kw==", "path": "microsoft.entityframeworkcore.design/7.0.0", "hashPath": "microsoft.entityframeworkcore.design.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eQiYygtR2xZ0Uy7KtiFRHpoEx/U8xNwbNRgu1pEJgSxbJLtg6tDL1y2YcIbSuIRSNEljXIIHq/apEhGm1QL70g==", "path": "microsoft.entityframeworkcore.relational/7.0.0", "hashPath": "microsoft.entityframeworkcore.relational.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tufYoEHetVeALPRqzCi4YukL+uoS0pBi/6m3uSCuM5NxO/tTbuizvvBRa3qJNYZOvF3K7m4lUmmcCymKr3JSSQ==", "path": "microsoft.entityframeworkcore.sqlite/7.0.0", "hashPath": "microsoft.entityframeworkcore.sqlite.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aUClrz1PT06fPDY+9f2IeDhYXj3/oPxM0r3I6syiyP3Th59hObVI0QsRu5+y7FbJIkO3NgyAi+e2vzWGhmBVbQ==", "path": "microsoft.entityframeworkcore.sqlite.core/7.0.0", "hashPath": "microsoft.entityframeworkcore.sqlite.core.7.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DtLJ0usm8NdPbRDxvNUBAYgnvqhodr/HPb461I+jrgHw5ZKF0vRTaokNth2Zy9xiw1ZTpT4c+S40f7AHWakODg==", "path": "microsoft.entityframeworkcore.tools/7.0.0", "hashPath": "microsoft.entityframeworkcore.tools.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IeimUd0TNbhB4ded3AbgBLQv2SnsiVugDyGV1MvspQFVlA07nDC7Zul7kcwH5jWN3JiTcp/ySE83AIJo8yfKjg==", "path": "microsoft.extensions.caching.abstractions/7.0.0", "hashPath": "microsoft.extensions.caching.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xpidBs2KCE2gw1JrD0quHE72kvCaI3xFql5/Peb2GRtUuZX+dYPoK/NTdVMiM67Svym0M0Df9A3xyU0FbMQhHw==", "path": "microsoft.extensions.caching.memory/7.0.0", "hashPath": "microsoft.extensions.caching.memory.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-f34u2eaqIjNO9YLHBz8rozVZ+TcFiFs0F3r7nUJd7FRkVSxk8u4OpoK226mi49MwexHOR2ibP9MFvRUaLilcQQ==", "path": "microsoft.extensions.configuration.abstractions/7.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-elNeOmkeX3eDVG6pYVeV82p29hr+UKDaBhrZyWvWLw/EVZSYEkZlQdkp0V39k/Xehs2Qa0mvoCvkVj3eQxNQ1Q==", "path": "microsoft.extensions.dependencyinjection/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h3j/QfmFN4S0w4C2A6X7arXij/M/OVw3uQHSOFxnND4DyAzO1F9eMX7Eti7lU/OkSthEE0WzRsfT/Dmx86jzCw==", "path": "microsoft.extensions.dependencyinjection.abstractions/7.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oONNYd71J3LzkWc4fUHl3SvMfiQMYUCo/mDHDEu76hYYxdhdrPYv6fvGv9nnKVyhE9P0h20AU8RZB5OOWQcAXg==", "path": "microsoft.extensions.dependencymodel/7.0.0", "hashPath": "microsoft.extensions.dependencymodel.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Nw2muoNrOG5U5qa2ZekXwudUn2BJcD41e65zwmDHb1fQegTX66UokLWZkJRpqSSHXDOWZ5V0iqhbxOEky91atA==", "path": "microsoft.extensions.logging/7.0.0", "hashPath": "microsoft.extensions.logging.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kmn78+LPVMOWeITUjIlfxUPDsI0R6G0RkeAMBmQxAJ7vBJn4q2dTva7pWi65ceN5vPGjJ9q/Uae2WKgvfktJAw==", "path": "microsoft.extensions.logging.abstractions/7.0.0", "hashPath": "microsoft.extensions.logging.abstractions.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lP1yBnTTU42cKpMozuafbvNtQ7QcBjr/CcK3bYOGEMH55Fjt+iecXjT6chR7vbgCMqy3PG3aNQSZgo/EuY/9qQ==", "path": "microsoft.extensions.options/7.0.0", "hashPath": "microsoft.extensions.options.7.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-um1KU5kxcRp3CNuI8o/GrZtD4AIOXDk+RLsytjZ9QPok3ttLUelLKpilVPuaFT3TFjOhSibUAso0odbOaCDj3Q==", "path": "microsoft.extensions.primitives/7.0.0", "hashPath": "microsoft.extensions.primitives.7.0.0.nupkg.sha512"}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"type": "package", "serviceable": true, "sha512": "sha512-0GvbEgDGcUQA9KuWcQU1WwYHXt1tBzNr1Nls/M57rM7NA/AndFwCaCEoJpJkmxRY7xLlPDBnmGp8h5+FNqUngg==", "path": "microsoft.net.illink.analyzers/7.0.100-1.23211.1", "hashPath": "microsoft.net.illink.analyzers.7.0.100-1.23211.1.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"type": "package", "serviceable": true, "sha512": "sha512-tvG8XZYLjT0o3WicCyKBZysVWo1jC9HdCFmNRmddx3WbAz0UCsd0qKZqpiEo99VLA8Re+FzWK51OcRldQPbt2Q==", "path": "microsoft.net.illink.tasks/7.0.100-1.23211.1", "hashPath": "microsoft.net.illink.tasks.7.0.100-1.23211.1.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-ilkvNhrTersLmIVAcDwwPqfhUFCg19Z1GVMvCSi3xk6Akq94f4qadLORQCq/T8+9JgMiPs+F/NECw5uauviaNw==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-A8EBepVqY2lnAp3a8jnhbgzF2tlj2S3HcJQGANTYg/TbYbKa8Z5cM1h74An/vy0svhfzT7tVY0sFmUglLgv+2g==", "path": "sqlitepclraw.core/2.1.2", "hashPath": "sqlitepclraw.core.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-zibGtku8M4Eea1R3ZCAxc86QbNvyEN17mAcQkvWKBuHvRpMiK2g5anG4R5Be7cWKSd1i6baYz8y4dMMAKcXKPg==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-lxCZarZdvAsMl2zw9bXHrXK6RxVhB4b23iTFhCOdHFhxfbsxLxWf+ocvswJwR/9Wh/E//ddMi+wJGqUKV7VwoA==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.2.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Memory/4.5.3": {"type": "package", "serviceable": true, "sha512": "sha512-3oDzvc/zzetpTKWMShs1AADwZjQ/36HnsufHRPcOjyRAAMLDlu2iD33MBI2opxnezcVUtXyqDXXjoFMOU9c7SA==", "path": "system.memory/4.5.3", "hashPath": "system.memory.4.5.3.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OP6umVGxc0Z0MvZQBVigj4/U31Pw72ITihDWP9WiWDm+q5aoe0GaJivsfYGq53o6dxH7DcXWiCTl7+0o2CGdmg==", "path": "system.text.encodings.web/7.0.0", "hashPath": "system.text.encodings.web.7.0.0.nupkg.sha512"}, "System.Text.Json/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DaGSsVqKsn/ia6RG8frjwmJonfos0srquhw09TlT8KRw5I43E+4gs+/bZj4K0vShJ5H9imCuXupb4RmS+dBy3w==", "path": "system.text.json/7.0.0", "hashPath": "system.text.json.7.0.0.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"], "win-x64-aot": ["win-aot", "win-x64", "win", "aot", "any", "base"], "win10-x64": ["win10", "win81-x64", "win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win10-x64-aot": ["win10-aot", "win10-x64", "win10", "win81-x64-aot", "win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win7-x64": ["win7", "win-x64", "win", "any", "base"], "win7-x64-aot": ["win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win8-x64": ["win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win8-x64-aot": ["win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"], "win81-x64": ["win81", "win8-x64", "win8", "win7-x64", "win7", "win-x64", "win", "any", "base"], "win81-x64-aot": ["win81-aot", "win81-x64", "win81", "win8-x64-aot", "win8-aot", "win8-x64", "win8", "win7-x64-aot", "win7-aot", "win7-x64", "win7", "win-x64-aot", "win-aot", "win-x64", "win", "aot", "any", "base"]}}