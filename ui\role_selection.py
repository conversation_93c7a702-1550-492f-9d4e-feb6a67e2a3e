#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - شاشة اختيار الدور
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QFrame, QSizePolicy, QSpacerItem
)
from PySide6.QtCore import (
    Qt, QPropertyAnimation, QEasingCurve,
    QSize, QTimer, Signal
)
from PySide6.QtGui import QFont, QColor, QPalette, QLinearGradient

class RoleSelectionScreen(QWidget):
    # Signals
    manager_selected = Signal()
    employee_selected = Signal()

    def __init__(self):
        super().__init__()
        self.setWindowTitle("معاريس الجهراء - اختيار الدور")
        self.setMinimumSize(800, 600)

        # Center the window
        self.center_on_screen()

        # Setup UI
        self.setup_ui()

    def center_on_screen(self):
        """Center the window on the screen."""
        from PySide6.QtWidgets import QApplication

        screen_geometry = QApplication.primaryScreen().geometry()
        x = (screen_geometry.width() - self.width()) // 2
        y = (screen_geometry.height() - self.height()) // 2

        self.move(x, y)

    def setup_ui(self):
        """Setup the UI components."""
        # Main layout
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Header with logo
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setStyleSheet("""
            #headerFrame {
                background-color: #B3E0FF;
                border-radius: 10px;
                padding: 10px;
            }
        """)

        header_layout = QHBoxLayout(header_frame)

        logo_label = QLabel("معاريس الجهراء")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_font = QFont("Traditional Arabic", 28, QFont.Bold)
        logo_label.setFont(logo_font)

        # Set colors (blue)
        palette = logo_label.palette()
        palette.setColor(QPalette.WindowText, QColor(0, 102, 204))  # Blue
        logo_label.setPalette(palette)

        header_layout.addWidget(logo_label)

        # Add header to main layout
        main_layout.addWidget(header_frame)

        # Add spacer
        main_layout.addSpacerItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # Role buttons layout
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(40)

        # Manager button
        self.manager_button = QPushButton("مدير")
        self.manager_button.setMinimumSize(200, 150)
        self.manager_button.setFont(QFont("Traditional Arabic", 20, QFont.Bold))
        self.manager_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border-radius: 15px;
                padding: 20px;
            }
            QPushButton:hover {
                background-color: #f4d03f;
            }
            QPushButton:pressed {
                background-color: #e67e22;
            }
        """)
        self.manager_button.clicked.connect(self.on_manager_selected)

        # Employee button
        self.employee_button = QPushButton("موظف")
        self.employee_button.setMinimumSize(200, 150)
        self.employee_button.setFont(QFont("Traditional Arabic", 20, QFont.Bold))
        self.employee_button.setStyleSheet("""
            QPushButton {
                background-color: #4682B4;
                color: white;
                border-radius: 15px;
                padding: 20px;
            }
            QPushButton:hover {
                background-color: #5793C5;
            }
            QPushButton:pressed {
                background-color: #3571A3;
            }
        """)
        self.employee_button.clicked.connect(self.on_employee_selected)

        # Add buttons to layout
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.manager_button)
        buttons_layout.addWidget(self.employee_button)
        buttons_layout.addStretch()

        # Add buttons layout to main layout
        main_layout.addLayout(buttons_layout)

        # Add spacer
        main_layout.addSpacerItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # Set main layout
        self.setLayout(main_layout)

    def on_manager_selected(self):
        """Handle manager button click."""
        self.animate_button(self.manager_button)
        QTimer.singleShot(300, self.manager_selected.emit)

    def on_employee_selected(self):
        """Handle employee button click."""
        self.animate_button(self.employee_button)
        QTimer.singleShot(300, self.employee_selected.emit)

    def animate_button(self, button):
        """Animate button click."""
        # Create animation for button press effect
        animation = QPropertyAnimation(button, b"geometry")
        animation.setDuration(100)

        # Get current geometry
        current_geometry = button.geometry()

        # Create a slightly smaller geometry for the animation
        smaller_geometry = current_geometry.adjusted(5, 5, -5, -5)

        # Set animation values
        animation.setStartValue(current_geometry)
        animation.setEndValue(smaller_geometry)
        animation.setEasingCurve(QEasingCurve.OutQuad)

        # Start animation
        animation.start()
