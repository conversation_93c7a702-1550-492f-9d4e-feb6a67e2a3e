<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاريس الجهراء - تطبيق سطح المكتب</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Noto Sans Arabic', Arial, sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 0;
        }
        .gradient-header {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 50%, #d68910 100%);
        }
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(243, 156, 18, 0.4);
        }
        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            transition: border-color 0.3s ease;
            text-align: right;
        }
        .input-field:focus {
            outline: none;
            border-color: #f39c12;
        }
        .stat-card {
            padding: 24px;
            border-radius: 12px;
            color: white;
            text-align: center;
        }
        .stat-card-blue { background: linear-gradient(135deg, #3498db, #2980b9); }
        .stat-card-green { background: linear-gradient(135deg, #27ae60, #229954); }
        .stat-card-orange { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .stat-card-red { background: linear-gradient(135deg, #e74c3c, #c0392b); }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #f39c12;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Desktop-specific enhancements */
        .desktop-title-bar {
            height: 40px;
            background: linear-gradient(135deg, #f39c12, #e67e22);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            -webkit-app-region: drag;
        }

        .desktop-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 16px;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .desktop-notification.show {
            transform: translateX(0);
        }

        /* Enhanced desktop styling */
        .nav-btn {
            position: relative;
            transition: all 0.2s ease;
        }

        .nav-btn:hover {
            background-color: rgba(243, 156, 18, 0.1);
        }

        .nav-btn.active {
            color: #f39c12 !important;
            border-bottom: 3px solid #f39c12;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Desktop Title Bar -->
    <div class="desktop-title-bar">
        <i data-lucide="crown" class="w-5 h-5 ml-2"></i>
        معاريس الجهراء - نظام إدارة العملاء والطلبات
    </div>

    <!-- Header -->
    <header class="gradient-header shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i data-lucide="crown" class="w-8 h-8 text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-white">معاريس الجهراء</h1>
                        <p class="text-orange-100 text-sm">تطبيق سطح المكتب - نظام إدارة العملاء والطلبات</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <div class="text-right">
                        <p class="text-white font-semibold">مدير النظام</p>
                        <p class="text-orange-100 text-sm">مرحباً بك</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-2 rounded-full">
                        <i data-lucide="user" class="w-6 h-6 text-white"></i>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="bg-white shadow-md">
        <div class="container mx-auto px-6">
            <div class="flex space-x-8 space-x-reverse">
                <button onclick="showSection('dashboard')" class="nav-btn active py-4 px-6 font-semibold">
                    <i data-lucide="home" class="w-5 h-5 inline ml-2"></i>
                    الرئيسية
                </button>
                <button onclick="showSection('customers')" class="nav-btn py-4 px-6 text-gray-600 hover:text-orange-600 font-semibold">
                    <i data-lucide="users" class="w-5 h-5 inline ml-2"></i>
                    العملاء
                </button>
                <button onclick="showSection('orders')" class="nav-btn py-4 px-6 text-gray-600 hover:text-orange-600 font-semibold">
                    <i data-lucide="shopping-cart" class="w-5 h-5 inline ml-2"></i>
                    الطلبات
                </button>
                <button onclick="showSection('debts')" class="nav-btn py-4 px-6 text-gray-600 hover:text-orange-600 font-semibold">
                    <i data-lucide="credit-card" class="w-5 h-5 inline ml-2"></i>
                    الديون
                </button>
                <button onclick="showSection('reports')" class="nav-btn py-4 px-6 text-gray-600 hover:text-orange-600 font-semibold">
                    <i data-lucide="bar-chart-3" class="w-5 h-5 inline ml-2"></i>
                    التقارير
                </button>
            </div>
        </div>
    </nav>

    <!-- Desktop Notification -->
    <div id="desktop-notification" class="desktop-notification">
        <div class="flex items-center">
            <i data-lucide="check-circle" class="w-5 h-5 text-green-500 ml-2"></i>
            <span id="notification-text">تم الحفظ بنجاح</span>
        </div>
    </div>

    <!-- Main Content -->
    <main class="container mx-auto px-6 py-8">
        <!-- Dashboard Section -->
        <div id="dashboard" class="section">
            <div class="fade-in">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">لوحة التحكم</h2>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="stat-card stat-card-blue">
                        <i data-lucide="shopping-cart" class="w-8 h-8 mx-auto mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">طلبات اليوم</h3>
                        <p class="text-3xl font-bold" id="daily-orders">-</p>
                    </div>
                    <div class="stat-card stat-card-green">
                        <i data-lucide="dollar-sign" class="w-8 h-8 mx-auto mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">مبيعات اليوم</h3>
                        <p class="text-3xl font-bold" id="daily-sales">-</p>
                    </div>
                    <div class="stat-card stat-card-orange">
                        <i data-lucide="users" class="w-8 h-8 mx-auto mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">إجمالي العملاء</h3>
                        <p class="text-3xl font-bold" id="total-customers">-</p>
                    </div>
                    <div class="stat-card stat-card-red">
                        <i data-lucide="credit-card" class="w-8 h-8 mx-auto mb-3"></i>
                        <h3 class="text-lg font-semibold mb-2">الديون المستحقة</h3>
                        <p class="text-3xl font-bold" id="total-debts">-</p>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="card p-6 text-center cursor-pointer" onclick="showSection('customers')">
                        <i data-lucide="user-plus" class="w-12 h-12 text-orange-500 mx-auto mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">إضافة عميل جديد</h3>
                        <p class="text-gray-600">إضافة عميل جديد مع قياساته</p>
                    </div>
                    <div class="card p-6 text-center cursor-pointer" onclick="showSection('orders')">
                        <i data-lucide="plus-circle" class="w-12 h-12 text-blue-500 mx-auto mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">إنشاء طلب جديد</h3>
                        <p class="text-gray-600">إضافة طلب جديد لعميل موجود</p>
                    </div>
                    <div class="card p-6 text-center cursor-pointer" onclick="showSection('reports')">
                        <i data-lucide="trending-up" class="w-12 h-12 text-green-500 mx-auto mb-4"></i>
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">عرض التقارير</h3>
                        <p class="text-gray-600">تقارير مفصلة عن الأداء</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customers Section -->
        <div id="customers" class="section hidden">
            <div class="fade-in">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-3xl font-bold text-gray-800">إدارة العملاء</h2>
                    <button onclick="showAddCustomerForm()" class="btn-primary">
                        <i data-lucide="plus" class="w-5 h-5 inline ml-2"></i>
                        إضافة عميل جديد
                    </button>
                </div>

                <!-- Search -->
                <div class="card p-4 mb-6">
                    <div class="relative">
                        <i data-lucide="search" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"></i>
                        <input type="text" id="customer-search" placeholder="البحث عن عميل بالاسم أو رقم الهاتف..."
                               class="input-field pr-10" onkeyup="searchCustomers()">
                    </div>
                </div>

                <!-- Customers List -->
                <div id="customers-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="loading mx-auto"></div>
                </div>
            </div>
        </div>

        <!-- Orders Section -->
        <div id="orders" class="section hidden">
            <div class="fade-in">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-3xl font-bold text-gray-800">إدارة الطلبات</h2>
                    <button onclick="showAddOrderForm()" class="btn-primary">
                        <i data-lucide="plus" class="w-5 h-5 inline ml-2"></i>
                        إضافة طلب جديد
                    </button>
                </div>

                <!-- Orders List -->
                <div id="orders-list" class="space-y-4">
                    <div class="loading mx-auto"></div>
                </div>
            </div>
        </div>

        <!-- Debts Section -->
        <div id="debts" class="section hidden">
            <div class="fade-in">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">إدارة الديون</h2>

                <!-- Debts List -->
                <div id="debts-list" class="space-y-4">
                    <div class="loading mx-auto"></div>
                </div>
            </div>
        </div>

        <!-- Reports Section -->
        <div id="reports" class="section hidden">
            <div class="fade-in">
                <h2 class="text-3xl font-bold text-gray-800 mb-6">التقارير والإحصائيات</h2>

                <!-- Daily Report -->
                <div class="card p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">تقرير طلبات اليوم</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4" id="daily-report">
                        <div class="loading mx-auto"></div>
                    </div>
                </div>

                <!-- Monthly Report -->
                <div class="card p-6">
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">تقرير طلبات الشهر</h3>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4" id="monthly-report">
                        <div class="loading mx-auto"></div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Customer Modal -->
    <div id="add-customer-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-screen overflow-y-auto">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-800">إضافة عميل جديد</h3>
                <button onclick="hideAddCustomerForm()" class="text-gray-500 hover:text-gray-700">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <form id="add-customer-form" onsubmit="submitCustomer(event)">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-gray-700 font-semibold mb-2">اسم العميل *</label>
                        <input type="text" id="customer-name" required class="input-field" placeholder="أدخل اسم العميل">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-semibold mb-2">رقم الهاتف *</label>
                        <input type="tel" id="customer-phone" required class="input-field" placeholder="أدخل رقم الهاتف">
                    </div>
                </div>

                <h4 class="text-lg font-semibold text-gray-800 mb-4">القياسات (سم)</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div>
                        <label class="block text-gray-700 font-semibold mb-2">الصدر</label>
                        <input type="number" id="customer-chest" class="input-field" placeholder="سم" step="0.1">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-semibold mb-2">الكتف</label>
                        <input type="number" id="customer-shoulder" class="input-field" placeholder="سم" step="0.1">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-semibold mb-2">اليد</label>
                        <input type="number" id="customer-hand" class="input-field" placeholder="سم" step="0.1">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-semibold mb-2">الخصر</label>
                        <input type="number" id="customer-waist" class="input-field" placeholder="سم" step="0.1">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-semibold mb-2">الورك</label>
                        <input type="number" id="customer-hip" class="input-field" placeholder="سم" step="0.1">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-semibold mb-2">الطول الداخلي</label>
                        <input type="number" id="customer-inseam" class="input-field" placeholder="سم" step="0.1">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-semibold mb-2">الرقبة</label>
                        <input type="number" id="customer-neck" class="input-field" placeholder="سم" step="0.1">
                    </div>
                    <div>
                        <label class="block text-gray-700 font-semibold mb-2">الطول الكامل</label>
                        <input type="number" id="customer-height" class="input-field" placeholder="سم" step="0.1">
                    </div>
                </div>

                <div class="flex justify-end space-x-4 space-x-reverse">
                    <button type="button" onclick="hideAddCustomerForm()" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary">
                        <i data-lucide="save" class="w-5 h-5 inline ml-2"></i>
                        حفظ العميل
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Order Modal -->
    <div id="add-order-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 w-full max-w-lg mx-4">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-800">إضافة طلب جديد</h3>
                <button onclick="hideAddOrderForm()" class="text-gray-500 hover:text-gray-700">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <form id="add-order-form" onsubmit="submitOrder(event)">
                <div class="mb-4">
                    <label class="block text-gray-700 font-semibold mb-2">العميل *</label>
                    <select id="order-customer" required class="input-field">
                        <option value="">اختر العميل</option>
                    </select>
                </div>

                <div class="mb-4">
                    <label class="block text-gray-700 font-semibold mb-2">نوع الطلب *</label>
                    <select id="order-type" required class="input-field">
                        <option value="">اختر نوع الطلب</option>
                        <option value="صيفي">صيفي</option>
                        <option value="شتوي">شتوي</option>
                        <option value="رسمي">رسمي</option>
                        <option value="كاجوال">كاجوال</option>
                    </select>
                </div>

                <div class="mb-4">
                    <label class="block text-gray-700 font-semibold mb-2">السعر الإجمالي (د.ك) *</label>
                    <input type="number" id="order-total" required class="input-field" placeholder="0.000" step="0.001" min="0">
                </div>

                <div class="mb-6">
                    <label class="block text-gray-700 font-semibold mb-2">المبلغ المدفوع (د.ك) *</label>
                    <input type="number" id="order-payment" required class="input-field" placeholder="0.000" step="0.001" min="0">
                </div>

                <div class="flex justify-end space-x-4 space-x-reverse">
                    <button type="button" onclick="hideAddOrderForm()" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50">
                        إلغاء
                    </button>
                    <button type="submit" class="btn-primary">
                        <i data-lucide="save" class="w-5 h-5 inline ml-2"></i>
                        حفظ الطلب
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Customer Details Modal -->
    <div id="customer-details-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-6 w-full max-w-4xl mx-4 max-h-screen overflow-y-auto">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-bold text-gray-800">تفاصيل العميل</h3>
                <button onclick="hideCustomerDetails()" class="text-gray-500 hover:text-gray-700">
                    <i data-lucide="x" class="w-6 h-6"></i>
                </button>
            </div>

            <div id="customer-details-content">
                <!-- Customer details will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // API Base URL for desktop app
        const API_BASE = 'http://localhost:8000/api';

        // Current section
        let currentSection = 'dashboard';

        // Show section with desktop enhancements
        function showSection(section) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(s => s.classList.add('hidden'));

            // Show selected section
            document.getElementById(section).classList.remove('hidden');

            // Update navigation with desktop styling
            document.querySelectorAll('.nav-btn').forEach(btn => {
                btn.classList.remove('active', 'text-orange-600');
                btn.classList.add('text-gray-600');
            });

            // Find and activate the clicked button
            const clickedBtn = event ? event.target.closest('.nav-btn') :
                              document.querySelector(`[onclick="showSection('${section}')"]`);
            if (clickedBtn) {
                clickedBtn.classList.remove('text-gray-600');
                clickedBtn.classList.add('active', 'text-orange-600');
            }

            currentSection = section;

            // Load section data
            if (section === 'customers') loadCustomers();
            else if (section === 'orders') loadOrders();
            else if (section === 'debts') loadDebts();
            else if (section === 'reports') loadReports();

            // Show desktop notification
            if (window.showDesktopNotification) {
                const sectionNames = {
                    'dashboard': 'الرئيسية',
                    'customers': 'العملاء',
                    'orders': 'الطلبات',
                    'debts': 'الديون',
                    'reports': 'التقارير'
                };
                window.showDesktopNotification(`تم الانتقال إلى قسم ${sectionNames[section]}`, 'info');
            }
        }

        // API Functions with desktop error handling
        async function apiGet(endpoint) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                if (window.showDesktopNotification) {
                    window.showDesktopNotification('خطأ في الاتصال بالخادم', 'error');
                }
                return null;
            }
        }

        async function apiPost(endpoint, data) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return await response.json();
            } catch (error) {
                console.error('API Error:', error);
                if (window.showDesktopNotification) {
                    window.showDesktopNotification('خطأ في حفظ البيانات', 'error');
                }
                return null;
            }
        }

        // Load Dashboard Data with desktop notifications
        async function loadDashboardData() {
            try {
                const [customers, dailyStats, monthlyStats, debts] = await Promise.all([
                    apiGet('/customers'),
                    apiGet('/statistics/daily'),
                    apiGet('/statistics/monthly'),
                    apiGet('/debts')
                ]);

                // Update dashboard stats
                document.getElementById('daily-orders').textContent = dailyStats?.total_orders || 0;
                document.getElementById('daily-sales').textContent = `${dailyStats?.total_amount || 0} د.ك`;
                document.getElementById('total-customers').textContent = customers?.length || 0;
                document.getElementById('total-debts').textContent = debts?.length || 0;

                // Show success notification
                if (window.showDesktopNotification && customers) {
                    window.showDesktopNotification('تم تحديث البيانات بنجاح', 'success');
                }
            } catch (error) {
                console.error('Error loading dashboard data:', error);
            }
        }

        // Load Customers with desktop enhancements
        async function loadCustomers() {
            const customers = await apiGet('/customers');
            const container = document.getElementById('customers-list');

            if (!customers || customers.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-500 col-span-full">لا توجد عملاء</p>';
                return;
            }

            container.innerHTML = customers.map(customer => `
                <div class="card p-6 desktop-fade-in cursor-pointer hover:shadow-lg transition-all" onclick="showCustomerDetails(${customer.id})">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="bg-orange-100 p-2 rounded-full ml-3">
                                <i data-lucide="user" class="w-5 h-5 text-orange-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800 hover:text-orange-600">${customer.name}</h3>
                                <p class="text-gray-600 text-sm">${customer.phone}</p>
                            </div>
                        </div>
                        <div class="text-gray-400">
                            <i data-lucide="eye" class="w-5 h-5"></i>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">الصدر:</span>
                            <span class="font-medium">${customer.chest || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الكتف:</span>
                            <span class="font-medium">${customer.shoulder || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الخصر:</span>
                            <span class="font-medium">${customer.waist || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الطول:</span>
                            <span class="font-medium">${customer.height || '-'} سم</span>
                        </div>
                    </div>
                </div>
            `).join('');

            // Re-initialize icons
            lucide.createIcons();
        }

        // Load Orders with desktop styling
        async function loadOrders() {
            const [orders, customers] = await Promise.all([
                apiGet('/orders'),
                apiGet('/customers')
            ]);

            const container = document.getElementById('orders-list');

            if (!orders || orders.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-500">لا توجد طلبات</p>';
                return;
            }

            const getCustomerName = (customerId) => {
                const customer = customers?.find(c => c.id === customerId);
                return customer?.name || 'غير معروف';
            };

            container.innerHTML = orders.map(order => `
                <div class="card p-6 desktop-fade-in">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="bg-blue-100 p-3 rounded-full">
                                <i data-lucide="package" class="w-6 h-6 text-blue-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">${getCustomerName(order.customer_id)}</h3>
                                <p class="text-gray-600 text-sm">${new Date(order.created_at).toLocaleDateString('ar-SA')}</p>
                            </div>
                        </div>
                        <div class="text-left">
                            <span class="px-3 py-1 rounded-full text-sm font-medium ${
                                order.order_type === 'صيفي'
                                    ? 'bg-yellow-100 text-yellow-800'
                                    : 'bg-blue-100 text-blue-800'
                            }">${order.order_type}</span>
                            <div class="text-sm text-gray-600 mt-2">
                                <div>الإجمالي: <span class="font-semibold">${order.total_price} د.ك</span></div>
                                <div>المدفوع: <span class="font-semibold text-green-600">${order.payment} د.ك</span></div>
                                <div>المتبقي: <span class="font-semibold ${order.remaining > 0 ? 'text-red-600' : 'text-green-600'}">${order.remaining} د.ك</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            lucide.createIcons();
        }

        // Load Debts
        async function loadDebts() {
            const [debts, customers] = await Promise.all([
                apiGet('/debts'),
                apiGet('/customers')
            ]);

            const container = document.getElementById('debts-list');

            if (!debts || debts.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-500">لا توجد ديون</p>';
                return;
            }

            const getCustomerName = (customerId) => {
                const customer = customers?.find(c => c.id === customerId);
                return customer?.name || 'غير معروف';
            };

            container.innerHTML = debts.map(debt => `
                <div class="card p-6 desktop-fade-in">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="bg-red-100 p-3 rounded-full">
                                <i data-lucide="credit-card" class="w-6 h-6 text-red-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800">${getCustomerName(debt.customer_id)}</h3>
                                <p class="text-gray-600 text-sm">${new Date(debt.created_at).toLocaleDateString('ar-SA')}</p>
                            </div>
                        </div>
                        <div class="text-left">
                            <div class="text-sm text-gray-600">
                                <div>المبلغ الأصلي: <span class="font-semibold">${debt.amount} د.ك</span></div>
                                <div>المدفوع: <span class="font-semibold text-green-600">${debt.payment} د.ك</span></div>
                                <div>المتبقي: <span class="font-semibold text-red-600">${debt.remaining} د.ك</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');

            lucide.createIcons();
        }

        // Load Reports
        async function loadReports() {
            const [dailyStats, monthlyStats] = await Promise.all([
                apiGet('/statistics/daily'),
                apiGet('/statistics/monthly')
            ]);

            // Daily Report
            document.getElementById('daily-report').innerHTML = `
                <div class="bg-blue-50 p-4 rounded-lg text-center desktop-fade-in">
                    <i data-lucide="shopping-cart" class="w-8 h-8 text-blue-500 mx-auto mb-2"></i>
                    <p class="text-blue-600 text-sm font-medium">عدد الطلبات</p>
                    <p class="text-2xl font-bold text-blue-800">${dailyStats?.total_orders || 0}</p>
                </div>
                <div class="bg-green-50 p-4 rounded-lg text-center desktop-fade-in">
                    <i data-lucide="dollar-sign" class="w-8 h-8 text-green-500 mx-auto mb-2"></i>
                    <p class="text-green-600 text-sm font-medium">إجمالي المبيعات</p>
                    <p class="text-2xl font-bold text-green-800">${dailyStats?.total_amount || 0} د.ك</p>
                </div>
                <div class="bg-orange-50 p-4 rounded-lg text-center desktop-fade-in">
                    <i data-lucide="trending-up" class="w-8 h-8 text-orange-500 mx-auto mb-2"></i>
                    <p class="text-orange-600 text-sm font-medium">المبلغ المدفوع</p>
                    <p class="text-2xl font-bold text-orange-800">${dailyStats?.paid_amount || 0} د.ك</p>
                </div>
                <div class="bg-red-50 p-4 rounded-lg text-center desktop-fade-in">
                    <i data-lucide="credit-card" class="w-8 h-8 text-red-500 mx-auto mb-2"></i>
                    <p class="text-red-600 text-sm font-medium">المبلغ المتبقي</p>
                    <p class="text-2xl font-bold text-red-800">${dailyStats?.remaining_amount || 0} د.ك</p>
                </div>
            `;

            // Monthly Report
            document.getElementById('monthly-report').innerHTML = `
                <div class="bg-purple-50 p-4 rounded-lg text-center desktop-fade-in">
                    <i data-lucide="shopping-cart" class="w-8 h-8 text-purple-500 mx-auto mb-2"></i>
                    <p class="text-purple-600 text-sm font-medium">عدد الطلبات</p>
                    <p class="text-2xl font-bold text-purple-800">${monthlyStats?.total_orders || 0}</p>
                </div>
                <div class="bg-indigo-50 p-4 rounded-lg text-center desktop-fade-in">
                    <i data-lucide="dollar-sign" class="w-8 h-8 text-indigo-500 mx-auto mb-2"></i>
                    <p class="text-indigo-600 text-sm font-medium">إجمالي المبيعات</p>
                    <p class="text-2xl font-bold text-indigo-800">${monthlyStats?.total_amount || 0} د.ك</p>
                </div>
                <div class="bg-pink-50 p-4 rounded-lg text-center desktop-fade-in">
                    <i data-lucide="trending-up" class="w-8 h-8 text-pink-500 mx-auto mb-2"></i>
                    <p class="text-pink-600 text-sm font-medium">المبلغ المدفوع</p>
                    <p class="text-2xl font-bold text-pink-800">${monthlyStats?.paid_amount || 0} د.ك</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded-lg text-center desktop-fade-in">
                    <i data-lucide="credit-card" class="w-8 h-8 text-yellow-500 mx-auto mb-2"></i>
                    <p class="text-yellow-600 text-sm font-medium">المبلغ المتبقي</p>
                    <p class="text-2xl font-bold text-yellow-800">${monthlyStats?.remaining_amount || 0} د.ك</p>
                </div>
            `;

            lucide.createIcons();
        }

        // Search Customers with desktop enhancements
        async function searchCustomers() {
            const query = document.getElementById('customer-search').value;
            if (query.length < 2) {
                loadCustomers();
                return;
            }

            const customers = await apiGet(`/customers/search/${encodeURIComponent(query)}`);
            const container = document.getElementById('customers-list');

            if (!customers || customers.length === 0) {
                container.innerHTML = '<p class="text-center text-gray-500 col-span-full">لا توجد نتائج</p>';
                return;
            }

            container.innerHTML = customers.map(customer => `
                <div class="card p-6 desktop-fade-in cursor-pointer hover:shadow-lg transition-all" onclick="showCustomerDetails(${customer.id})">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center">
                            <div class="bg-orange-100 p-2 rounded-full ml-3">
                                <i data-lucide="user" class="w-5 h-5 text-orange-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-800 hover:text-orange-600">${customer.name}</h3>
                                <p class="text-gray-600 text-sm">${customer.phone}</p>
                            </div>
                        </div>
                        <div class="text-gray-400">
                            <i data-lucide="eye" class="w-5 h-5"></i>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">الصدر:</span>
                            <span class="font-medium">${customer.chest || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الكتف:</span>
                            <span class="font-medium">${customer.shoulder || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الخصر:</span>
                            <span class="font-medium">${customer.waist || '-'} سم</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الطول:</span>
                            <span class="font-medium">${customer.height || '-'} سم</span>
                        </div>
                    </div>
                </div>
            `).join('');

            lucide.createIcons();
        }

        // Modal form functions
        function showAddCustomerForm() {
            document.getElementById('add-customer-modal').classList.remove('hidden');
            // Clear form
            document.getElementById('add-customer-form').reset();
            lucide.createIcons();
        }

        function hideAddCustomerForm() {
            document.getElementById('add-customer-modal').classList.add('hidden');
        }

        async function showAddOrderForm() {
            // Load customers for the dropdown
            const customers = await apiGet('/customers');
            const customerSelect = document.getElementById('order-customer');

            customerSelect.innerHTML = '<option value="">اختر العميل</option>';
            if (customers && customers.length > 0) {
                customers.forEach(customer => {
                    customerSelect.innerHTML += `<option value="${customer.id}">${customer.name} - ${customer.phone}</option>`;
                });
            }

            document.getElementById('add-order-modal').classList.remove('hidden');
            // Clear form
            document.getElementById('add-order-form').reset();
            lucide.createIcons();
        }

        function hideAddOrderForm() {
            document.getElementById('add-order-modal').classList.add('hidden');
        }

        // Submit customer form
        async function submitCustomer(event) {
            event.preventDefault();

            const customerData = {
                name: document.getElementById('customer-name').value,
                phone: document.getElementById('customer-phone').value,
                chest: parseFloat(document.getElementById('customer-chest').value) || null,
                shoulder: parseFloat(document.getElementById('customer-shoulder').value) || null,
                hand: parseFloat(document.getElementById('customer-hand').value) || null,
                waist: parseFloat(document.getElementById('customer-waist').value) || null,
                hip: parseFloat(document.getElementById('customer-hip').value) || null,
                inseam: parseFloat(document.getElementById('customer-inseam').value) || null,
                neck: parseFloat(document.getElementById('customer-neck').value) || null,
                height: parseFloat(document.getElementById('customer-height').value) || null
            };

            const result = await apiPost('/customers', customerData);
            if (result) {
                hideAddCustomerForm();
                if (window.showDesktopNotification) {
                    window.showDesktopNotification('تم إضافة العميل بنجاح', 'success');
                }
                // Reload customers if we're on the customers page
                if (currentSection === 'customers') {
                    loadCustomers();
                }
                // Update dashboard data
                loadDashboardData();
            }
        }

        // Submit order form
        async function submitOrder(event) {
            event.preventDefault();

            const orderData = {
                customer_id: parseInt(document.getElementById('order-customer').value),
                order_type: document.getElementById('order-type').value,
                total_price: parseFloat(document.getElementById('order-total').value),
                payment: parseFloat(document.getElementById('order-payment').value)
            };

            const result = await apiPost('/orders', orderData);
            if (result) {
                hideAddOrderForm();
                if (window.showDesktopNotification) {
                    window.showDesktopNotification('تم إضافة الطلب بنجاح', 'success');
                }
                // Reload orders if we're on the orders page
                if (currentSection === 'orders') {
                    loadOrders();
                }
                // Reload debts if we're on the debts page (in case there's remaining balance)
                if (currentSection === 'debts') {
                    loadDebts();
                }
                // Update dashboard data
                loadDashboardData();
            }
        }

        // Show customer details
        async function showCustomerDetails(customerId) {
            const customer = await apiGet(`/customers/${customerId}`);
            const orders = await apiGet(`/orders`);

            if (!customer) {
                if (window.showDesktopNotification) {
                    window.showDesktopNotification('خطأ في تحميل بيانات العميل', 'error');
                }
                return;
            }

            // Filter orders for this customer
            const customerOrders = orders ? orders.filter(order => order.customer_id === customerId) : [];

            const content = document.getElementById('customer-details-content');
            content.innerHTML = `
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Customer Info -->
                    <div class="card p-6">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                            <i data-lucide="user" class="w-5 h-5 ml-2 text-orange-600"></i>
                            معلومات العميل
                        </h4>
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600">الاسم:</span>
                                <span class="font-semibold">${customer.name}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">رقم الهاتف:</span>
                                <span class="font-semibold">${customer.phone}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">تاريخ التسجيل:</span>
                                <span class="font-semibold">${new Date(customer.created_at).toLocaleDateString('ar-SA')}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Measurements -->
                    <div class="card p-6">
                        <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                            <i data-lucide="ruler" class="w-5 h-5 ml-2 text-blue-600"></i>
                            القياسات (سم)
                        </h4>
                        <div class="grid grid-cols-2 gap-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">الصدر:</span>
                                <span class="font-semibold">${customer.chest || '-'} سم</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الكتف:</span>
                                <span class="font-semibold">${customer.shoulder || '-'} سم</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">اليد:</span>
                                <span class="font-semibold">${customer.hand || '-'} سم</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الخصر:</span>
                                <span class="font-semibold">${customer.waist || '-'} سم</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الورك:</span>
                                <span class="font-semibold">${customer.hip || '-'} سم</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الطول الداخلي:</span>
                                <span class="font-semibold">${customer.inseam || '-'} سم</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الرقبة:</span>
                                <span class="font-semibold">${customer.neck || '-'} سم</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">الطول الكامل:</span>
                                <span class="font-semibold">${customer.height || '-'} سم</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Orders -->
                <div class="card p-6 mt-6">
                    <h4 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <i data-lucide="shopping-cart" class="w-5 h-5 ml-2 text-green-600"></i>
                        طلبات العميل (${customerOrders.length})
                    </h4>
                    ${customerOrders.length === 0 ?
                        '<p class="text-gray-500 text-center py-4">لا توجد طلبات لهذا العميل</p>' :
                        `<div class="space-y-3">
                            ${customerOrders.map(order => `
                                <div class="border rounded-lg p-4 bg-gray-50">
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <span class="px-3 py-1 rounded-full text-sm font-medium ${
                                                order.order_type === 'صيفي'
                                                    ? 'bg-yellow-100 text-yellow-800'
                                                    : 'bg-blue-100 text-blue-800'
                                            }">${order.order_type}</span>
                                            <span class="text-gray-600 text-sm mr-3">${new Date(order.created_at).toLocaleDateString('ar-SA')}</span>
                                        </div>
                                        <div class="text-left">
                                            <div class="text-sm text-gray-600">
                                                <div>الإجمالي: <span class="font-semibold">${order.total_price} د.ك</span></div>
                                                <div>المدفوع: <span class="font-semibold text-green-600">${order.payment} د.ك</span></div>
                                                <div>المتبقي: <span class="font-semibold ${order.remaining > 0 ? 'text-red-600' : 'text-green-600'}">${order.remaining} د.ك</span></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>`
                    }
                </div>
            `;

            document.getElementById('customer-details-modal').classList.remove('hidden');
            lucide.createIcons();
        }

        function hideCustomerDetails() {
            document.getElementById('customer-details-modal').classList.add('hidden');
        }
    </script>
    <script>
        // Initialize application with error handling
        try {
            // Initialize Lucide icons
            lucide.createIcons();

            // Initialize desktop features
            if (window.desktopFeatures) {
                window.desktopFeatures.addDesktopStyles();
                window.desktopFeatures.setupKeyboardShortcuts();
            }

            // Setup menu action listener
            if (window.electronAPI) {
                window.electronAPI.onMenuAction((action) => {
                    try {
                        switch(action) {
                            case 'new-customer':
                                showAddCustomerForm();
                                break;
                            case 'new-order':
                                showAddOrderForm();
                                break;
                            case 'show-dashboard':
                                showSection('dashboard');
                                break;
                            case 'show-customers':
                                showSection('customers');
                                break;
                            case 'show-orders':
                                showSection('orders');
                                break;
                            case 'show-debts':
                                showSection('debts');
                                break;
                            case 'show-reports':
                                showSection('reports');
                                break;
                            default:
                                console.warn('Unknown menu action:', action);
                        }
                    } catch (error) {
                        console.error('Error handling menu action:', error);
                        if (window.showDesktopNotification) {
                            window.showDesktopNotification('خطأ في تنفيذ العملية', 'error');
                        }
                    }
                });
            }

            // Load initial data
            loadDashboardData();

        } catch (error) {
            console.error('Error initializing application:', error);
            if (window.showDesktopNotification) {
                window.showDesktopNotification('خطأ في تشغيل التطبيق', 'error');
            }
        }

        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            if (window.showDesktopNotification) {
                window.showDesktopNotification('حدث خطأ غير متوقع', 'error');
            }
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            if (window.showDesktopNotification) {
                window.showDesktopNotification('خطأ في معالجة البيانات', 'error');
            }
        });

        // Desktop notification function
        window.showDesktopNotification = function(message, type = 'success') {
            const notification = document.getElementById('desktop-notification');
            const text = document.getElementById('notification-text');
            const icon = notification.querySelector('i');

            text.textContent = message;

            // Update icon based on type
            if (type === 'success') {
                icon.setAttribute('data-lucide', 'check-circle');
                icon.className = 'w-5 h-5 text-green-500 ml-2';
            } else if (type === 'error') {
                icon.setAttribute('data-lucide', 'x-circle');
                icon.className = 'w-5 h-5 text-red-500 ml-2';
            } else if (type === 'info') {
                icon.setAttribute('data-lucide', 'info');
                icon.className = 'w-5 h-5 text-blue-500 ml-2';
            }

            // Show notification
            notification.classList.add('show');
            lucide.createIcons();

            // Hide after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        };
    </script>
</body>
</html>
