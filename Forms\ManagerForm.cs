using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using MaarisAlJahra.Helpers;
using FontAwesome.Sharp;

namespace MaarisAlJahra.Forms
{
    public partial class ManagerForm : Form
    {
        private Panel workersPanel;
        private Panel ordersPanel;
        private Panel debtsPanel;
        private Label titleLabel;

        public ManagerForm()
        {
            InitializeComponent();
            SetupForm();
            SetupControls();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 600);
            this.FormBorderStyle = FormBorderStyle.None;
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Name = "ManagerForm";
            this.Text = "لوحة تحكم المدير";

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer, true);

            StyleHelper.ApplyGradientBackground(this);
        }

        private void SetupControls()
        {
            // Title Label
            titleLabel = new Label
            {
                Text = "لوحة تحكم المدير",
                Font = new Font("Segoe UI", 36, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Workers Panel
            workersPanel = CreateManagementPanel("إدارة العمال", IconChar.Users, StyleHelper.AccentColor);
            workersPanel.Click += async (s, e) =>
            {
                await AnimationHelper.ScaleButton(workersPanel, 1.1f, 200);
                var workersForm = new WorkersForm();
                workersForm.ShowDialog();
            };

            // Orders Panel
            ordersPanel = CreateManagementPanel("إدارة الطلبات", IconChar.ShoppingCart, StyleHelper.SuccessColor);
            ordersPanel.Click += async (s, e) =>
            {
                await AnimationHelper.ScaleButton(ordersPanel, 1.1f, 200);
                var ordersForm = new OrdersForm();
                ordersForm.ShowDialog();
            };

            // Debts Panel
            debtsPanel = CreateManagementPanel("إدارة الديون", IconChar.CreditCard, StyleHelper.WarningColor);
            debtsPanel.Click += async (s, e) =>
            {
                await AnimationHelper.ScaleButton(debtsPanel, 1.1f, 200);
                var debtsForm = new DebtsForm();
                debtsForm.ShowDialog();
            };

            // Position controls
            PositionControls();

            this.Controls.Add(titleLabel);
            this.Controls.Add(workersPanel);
            this.Controls.Add(ordersPanel);
            this.Controls.Add(debtsPanel);

            // Handle resize
            this.Resize += (s, e) => PositionControls();
        }

        private Panel CreateManagementPanel(string title, IconChar icon, Color color)
        {
            var panel = new Panel
            {
                Size = new Size(350, 250),
                BackColor = color,
                Cursor = Cursors.Hand
            };

            StyleHelper.ApplyRoundedCorners(panel, 20);

            // Icon
            var iconLabel = new IconPictureBox
            {
                IconChar = icon,
                IconColor = Color.White,
                IconSize = 80,
                Size = new Size(80, 80),
                BackColor = Color.Transparent
            };

            // Title
            var titleLabel = new Label
            {
                Text = title,
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Position elements within panel
            iconLabel.Location = new Point((panel.Width - iconLabel.Width) / 2, 50);
            titleLabel.Location = new Point((panel.Width - titleLabel.Width) / 2, iconLabel.Bottom + 30);

            panel.Controls.Add(iconLabel);
            panel.Controls.Add(titleLabel);

            // Hover effects
            panel.MouseEnter += (s, e) =>
            {
                panel.BackColor = ControlPaint.Light(color, 0.2f);
            };

            panel.MouseLeave += (s, e) =>
            {
                panel.BackColor = color;
            };

            // Add gradient header
            panel.Paint += (s, e) =>
            {
                var headerRect = new Rectangle(0, 0, panel.Width, 40);
                using (var brush = new LinearGradientBrush(headerRect,
                    ControlPaint.Light(color, 0.3f), color, LinearGradientMode.Vertical))
                {
                    e.Graphics.FillRectangle(brush, headerRect);
                }
            };

            return panel;
        }

        private void PositionControls()
        {
            // Center title
            titleLabel.Location = new Point(
                (this.Width - titleLabel.Width) / 2,
                100
            );

            // Position panels in a row
            var totalPanelWidth = workersPanel.Width * 3 + 100; // 50px spacing between panels
            var startX = (this.Width - totalPanelWidth) / 2;
            var panelY = titleLabel.Bottom + 80;

            workersPanel.Location = new Point(startX, panelY);
            ordersPanel.Location = new Point(startX + workersPanel.Width + 50, panelY);
            debtsPanel.Location = new Point(startX + (workersPanel.Width + 50) * 2, panelY);
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            _ = AnimationHelper.FadeIn(this, 500);
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                this.Close();
                Application.Exit();
            }
            base.OnKeyDown(e);
        }
    }
}
