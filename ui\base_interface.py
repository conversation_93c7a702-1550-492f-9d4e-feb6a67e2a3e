#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - الواجهة الأساسية
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QFrame, QSizePolicy, QSpacerItem, QLineEdit,
    QListWidget, QGridLayout, QComboBox, QScrollArea, QTabWidget
)
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QFont, QColor, QPalette, QIcon

from database import Database

class BaseInterface(QMainWindow):
    def __init__(self, role="مستخدم"):
        super().__init__()
        self.role = role
        self.setWindowTitle(f"معاريس الجهراء - {role}")
        self.setMinimumSize(1200, 800)

        # Initialize database
        self.db = Database()

        # Center the window
        self.center_on_screen()

        # Setup UI
        self.setup_ui()

    def center_on_screen(self):
        """Center the window on the screen."""
        from PySide6.QtWidgets import QApplication

        screen_geometry = QApplication.primaryScreen().geometry()
        x = (screen_geometry.width() - self.width()) // 2
        y = (screen_geometry.height() - self.height()) // 2

        self.move(x, y)

    def setup_ui(self):
        """Setup the UI components."""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Header with logo
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setStyleSheet("""
            #headerFrame {
                background-color: #B3E0FF;
                border-radius: 10px;
                padding: 10px;
            }
        """)
        header_frame.setMaximumHeight(80)

        header_layout = QHBoxLayout(header_frame)

        # Back button
        self.back_button = QPushButton("رجوع")
        self.back_button.setFont(QFont("Traditional Arabic", 12, QFont.Bold))
        self.back_button.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                border-radius: 4px;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #78909C;
            }
            QPushButton:pressed {
                background-color: #455A64;
            }
        """)
        self.back_button.setFixedWidth(80)
        self.back_button.clicked.connect(self.go_back)

        logo_label = QLabel("معاريس الجهراء")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_font = QFont("Traditional Arabic", 24, QFont.Bold)
        logo_label.setFont(logo_font)

        # Set colors (blue)
        palette = logo_label.palette()
        palette.setColor(QPalette.WindowText, QColor(0, 102, 204))  # Blue
        logo_label.setPalette(palette)

        role_label = QLabel(f"الدور: {self.role}")
        role_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        role_label.setFont(QFont("Traditional Arabic", 14))

        header_layout.addWidget(self.back_button)
        header_layout.addWidget(logo_label)
        header_layout.addWidget(role_label)

        # Add header to main layout
        main_layout.addWidget(header_frame)

        # Create tab widget for the three main sections
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Traditional Arabic", 14, QFont.Bold))
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #C4C4C4;
                border-radius: 5px;
                padding: 5px;
            }
            QTabBar::tab {
                background-color: #E0E0E0;
                border: 1px solid #C4C4C4;
                border-bottom-color: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 8px 12px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #FFFFFF;
                border-bottom-color: #FFFFFF;
            }
            QTabBar::tab:hover {
                background-color: #F0F0F0;
            }
        """)

        # Create the three tabs
        self.customers_tab = QWidget()
        self.orders_tab = QWidget()
        self.debts_tab = QWidget()

        # Setup each tab
        self.setup_customers_tab()
        self.setup_orders_tab()
        self.setup_debts_tab()

        # Add tabs to tab widget
        self.tab_widget.addTab(self.customers_tab, "العملاء")
        self.tab_widget.addTab(self.orders_tab, "الطلبات")
        self.tab_widget.addTab(self.debts_tab, "الديون")

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

    def setup_customers_tab(self):
        """Setup the customers tab."""
        # Main layout for customers tab
        layout = QHBoxLayout(self.customers_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # Left panel - Customer list
        left_panel = QFrame()
        left_panel.setFrameShape(QFrame.StyledPanel)
        left_panel.setMaximumWidth(300)

        left_layout = QVBoxLayout(left_panel)

        # Search bar
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        self.customer_search = QLineEdit()
        self.customer_search.setPlaceholderText("بحث بالاسم أو رقم الهاتف")
        self.customer_search.textChanged.connect(self.search_customers)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.customer_search)

        # Customer list
        self.customer_list = QListWidget()
        self.customer_list.setFont(QFont("Traditional Arabic", 12))
        self.customer_list.currentItemChanged.connect(self.on_customer_selected)

        # Add widgets to left layout
        left_layout.addLayout(search_layout)
        left_layout.addWidget(QLabel("العملاء:"))
        left_layout.addWidget(self.customer_list)

        # Right panel - Customer details
        right_panel = QFrame()
        right_panel.setFrameShape(QFrame.StyledPanel)

        right_layout = QVBoxLayout(right_panel)

        # Customer details form
        form_layout = QGridLayout()

        # Name and phone
        form_layout.addWidget(QLabel("الاسم:"), 0, 0)
        self.customer_name = QLineEdit()
        form_layout.addWidget(self.customer_name, 0, 1)

        form_layout.addWidget(QLabel("الهاتف:"), 1, 0)
        self.customer_phone = QLineEdit()
        form_layout.addWidget(self.customer_phone, 1, 1)

        # Measurements
        measurements_label = QLabel("القياسات:")
        measurements_label.setFont(QFont("Traditional Arabic", 14, QFont.Bold))
        form_layout.addWidget(measurements_label, 2, 0, 1, 2)

        # Chest and shoulder
        form_layout.addWidget(QLabel("الصدر:"), 3, 0)
        self.customer_chest = QLineEdit()
        form_layout.addWidget(self.customer_chest, 3, 1)

        form_layout.addWidget(QLabel("الكتف:"), 4, 0)
        self.customer_shoulder = QLineEdit()
        form_layout.addWidget(self.customer_shoulder, 4, 1)

        # Hand and waist
        form_layout.addWidget(QLabel("اليد:"), 5, 0)
        self.customer_hand = QLineEdit()
        form_layout.addWidget(self.customer_hand, 5, 1)

        form_layout.addWidget(QLabel("الخصر:"), 6, 0)
        self.customer_waist = QLineEdit()
        form_layout.addWidget(self.customer_waist, 6, 1)

        # Hip and inseam
        form_layout.addWidget(QLabel("الورك:"), 7, 0)
        self.customer_hip = QLineEdit()
        form_layout.addWidget(self.customer_hip, 7, 1)

        form_layout.addWidget(QLabel("طول الساق:"), 8, 0)
        self.customer_inseam = QLineEdit()
        form_layout.addWidget(self.customer_inseam, 8, 1)

        # Neck and height
        form_layout.addWidget(QLabel("الرقبة:"), 9, 0)
        self.customer_neck = QLineEdit()
        form_layout.addWidget(self.customer_neck, 9, 1)

        form_layout.addWidget(QLabel("الطول:"), 10, 0)
        self.customer_height = QLineEdit()
        form_layout.addWidget(self.customer_height, 10, 1)

        # Buttons
        buttons_layout = QHBoxLayout()

        self.add_customer_btn = QPushButton("إضافة عميل")
        self.add_customer_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.add_customer_btn.clicked.connect(self.add_customer)

        self.edit_customer_btn = QPushButton("تعديل")
        self.edit_customer_btn.setStyleSheet("background-color: #2196F3; color: white;")
        self.edit_customer_btn.clicked.connect(self.edit_customer)

        self.delete_customer_btn = QPushButton("حذف")
        self.delete_customer_btn.setStyleSheet("background-color: #F44336; color: white;")
        self.delete_customer_btn.clicked.connect(self.delete_customer)

        self.save_customer_btn = QPushButton("حفظ")
        self.save_customer_btn.setStyleSheet("background-color: #9C27B0; color: white;")
        self.save_customer_btn.clicked.connect(self.save_customer)

        buttons_layout.addWidget(self.add_customer_btn)
        buttons_layout.addWidget(self.edit_customer_btn)
        buttons_layout.addWidget(self.delete_customer_btn)
        buttons_layout.addWidget(self.save_customer_btn)

        # Add layouts to right panel
        right_layout.addLayout(form_layout)
        right_layout.addLayout(buttons_layout)

        # Add panels to main layout
        layout.addWidget(left_panel)
        layout.addWidget(right_panel)

    def setup_orders_tab(self):
        """Setup the orders tab."""
        # Main layout for orders tab
        layout = QHBoxLayout(self.orders_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # Left panel - Customer list and statistics
        left_panel = QFrame()
        left_panel.setFrameShape(QFrame.StyledPanel)
        left_panel.setMaximumWidth(300)

        left_layout = QVBoxLayout(left_panel)

        # Statistics section
        stats_group = QFrame()
        stats_group.setFrameShape(QFrame.StyledPanel)
        stats_group.setStyleSheet("""
            QFrame {
                background-color: #F5F5F5;
                border-radius: 5px;
                padding: 5px;
            }
        """)

        stats_layout = QVBoxLayout(stats_group)

        # Daily statistics
        daily_label = QLabel("إحصائيات اليوم:")
        daily_label.setFont(QFont("Traditional Arabic", 12, QFont.Bold))
        stats_layout.addWidget(daily_label)

        daily_grid = QGridLayout()
        daily_grid.addWidget(QLabel("إجمالي المبيعات:"), 0, 0)
        self.daily_total_label = QLabel("0")
        daily_grid.addWidget(self.daily_total_label, 0, 1)

        daily_grid.addWidget(QLabel("المدفوع:"), 1, 0)
        self.daily_paid_label = QLabel("0")
        daily_grid.addWidget(self.daily_paid_label, 1, 1)

        daily_grid.addWidget(QLabel("المتبقي:"), 2, 0)
        self.daily_remaining_label = QLabel("0")
        daily_grid.addWidget(self.daily_remaining_label, 2, 1)

        stats_layout.addLayout(daily_grid)

        # Monthly statistics
        monthly_label = QLabel("إحصائيات الشهر:")
        monthly_label.setFont(QFont("Traditional Arabic", 12, QFont.Bold))
        stats_layout.addWidget(monthly_label)

        monthly_grid = QGridLayout()
        monthly_grid.addWidget(QLabel("إجمالي المبيعات:"), 0, 0)
        self.monthly_total_label = QLabel("0")
        monthly_grid.addWidget(self.monthly_total_label, 0, 1)

        monthly_grid.addWidget(QLabel("المدفوع:"), 1, 0)
        self.monthly_paid_label = QLabel("0")
        monthly_grid.addWidget(self.monthly_paid_label, 1, 1)

        monthly_grid.addWidget(QLabel("المتبقي:"), 2, 0)
        self.monthly_remaining_label = QLabel("0")
        monthly_grid.addWidget(self.monthly_remaining_label, 2, 1)

        stats_layout.addLayout(monthly_grid)

        # Add stats group to left layout
        left_layout.addWidget(stats_group)

        # Search bar for customer
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث عن عميل:")
        self.order_customer_search = QLineEdit()
        self.order_customer_search.setPlaceholderText("بحث بالاسم أو رقم الهاتف")
        self.order_customer_search.textChanged.connect(self.search_customers_for_order)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.order_customer_search)

        # Customer list
        self.order_customer_list = QListWidget()
        self.order_customer_list.setFont(QFont("Traditional Arabic", 12))
        self.order_customer_list.currentItemChanged.connect(self.on_order_customer_selected)

        # Add widgets to left layout
        left_layout.addLayout(search_layout)
        left_layout.addWidget(QLabel("العملاء:"))
        left_layout.addWidget(self.order_customer_list)

        # Refresh button for statistics
        self.refresh_stats_btn = QPushButton("تحديث الإحصائيات")
        self.refresh_stats_btn.setStyleSheet("background-color: #607D8B; color: white;")
        self.refresh_stats_btn.clicked.connect(self.refresh_order_statistics)
        left_layout.addWidget(self.refresh_stats_btn)

        # Right panel - Order details and list
        right_panel = QFrame()
        right_panel.setFrameShape(QFrame.StyledPanel)

        right_layout = QVBoxLayout(right_panel)

        # Order form
        form_group = QFrame()
        form_group.setFrameShape(QFrame.StyledPanel)
        form_layout = QGridLayout(form_group)

        # Customer info
        form_layout.addWidget(QLabel("العميل:"), 0, 0)
        self.order_customer_name = QLabel("لم يتم اختيار عميل")
        form_layout.addWidget(self.order_customer_name, 0, 1)

        # Order type
        form_layout.addWidget(QLabel("نوع الطلب:"), 1, 0)
        self.order_type = QComboBox()
        self.order_type.addItems(["صيفي", "شتوي"])
        form_layout.addWidget(self.order_type, 1, 1)

        # Total price
        form_layout.addWidget(QLabel("السعر الإجمالي:"), 2, 0)
        self.order_total_price = QLineEdit()
        form_layout.addWidget(self.order_total_price, 2, 1)

        # Payment
        form_layout.addWidget(QLabel("المدفوع:"), 3, 0)
        self.order_payment = QLineEdit()
        self.order_payment.textChanged.connect(self.calculate_remaining)
        form_layout.addWidget(self.order_payment, 3, 1)

        # Remaining
        form_layout.addWidget(QLabel("المتبقي:"), 4, 0)
        self.order_remaining = QLabel("0")
        form_layout.addWidget(self.order_remaining, 4, 1)

        # Order ID (hidden)
        self.current_order_id = None

        # Buttons
        buttons_layout = QHBoxLayout()

        self.add_order_btn = QPushButton("إضافة طلب")
        self.add_order_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.add_order_btn.clicked.connect(self.add_order)

        self.edit_order_btn = QPushButton("تعديل الطلب")
        self.edit_order_btn.setStyleSheet("background-color: #2196F3; color: white;")
        self.edit_order_btn.clicked.connect(self.edit_order)

        self.delete_order_btn = QPushButton("حذف الطلب")
        self.delete_order_btn.setStyleSheet("background-color: #F44336; color: white;")
        self.delete_order_btn.clicked.connect(self.delete_order)

        self.save_order_btn = QPushButton("حفظ الطلب")
        self.save_order_btn.setStyleSheet("background-color: #9C27B0; color: white;")
        self.save_order_btn.clicked.connect(self.save_order)

        buttons_layout.addWidget(self.add_order_btn)
        buttons_layout.addWidget(self.edit_order_btn)
        buttons_layout.addWidget(self.delete_order_btn)
        buttons_layout.addWidget(self.save_order_btn)

        # Add form and buttons to right layout
        right_layout.addWidget(form_group)
        right_layout.addLayout(buttons_layout)

        # Orders list
        orders_label = QLabel("طلبات العميل:")
        orders_label.setFont(QFont("Traditional Arabic", 14, QFont.Bold))
        right_layout.addWidget(orders_label)

        self.orders_list = QListWidget()
        self.orders_list.setFont(QFont("Traditional Arabic", 12))
        self.orders_list.currentItemChanged.connect(self.on_order_selected)
        right_layout.addWidget(self.orders_list)

        # Add panels to main layout
        layout.addWidget(left_panel)
        layout.addWidget(right_panel)

    def setup_debts_tab(self):
        """Setup the debts tab."""
        # Main layout for debts tab
        layout = QVBoxLayout(self.debts_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # Search bar for customer
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث عن عميل:")
        self.debt_customer_search = QLineEdit()
        self.debt_customer_search.setPlaceholderText("بحث بالاسم أو رقم الهاتف")
        self.debt_customer_search.textChanged.connect(self.search_customers_with_debt)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.debt_customer_search)

        # Customer list
        self.debt_customer_list = QListWidget()
        self.debt_customer_list.setFont(QFont("Traditional Arabic", 12))
        self.debt_customer_list.currentItemChanged.connect(self.on_debt_customer_selected)

        # Debt form
        form_layout = QGridLayout()

        # Customer info
        form_layout.addWidget(QLabel("العميل:"), 0, 0)
        self.debt_customer_name = QLabel("لم يتم اختيار عميل")
        form_layout.addWidget(self.debt_customer_name, 0, 1)

        # Total due
        form_layout.addWidget(QLabel("إجمالي المستحق:"), 1, 0)
        self.debt_total_due = QLineEdit()
        form_layout.addWidget(self.debt_total_due, 1, 1)

        # Paid
        form_layout.addWidget(QLabel("المدفوع:"), 2, 0)
        self.debt_paid = QLineEdit()
        self.debt_paid.textChanged.connect(self.calculate_debt_remaining)
        form_layout.addWidget(self.debt_paid, 2, 1)

        # Remaining
        form_layout.addWidget(QLabel("المتبقي:"), 3, 0)
        self.debt_remaining = QLabel("0")
        form_layout.addWidget(self.debt_remaining, 3, 1)

        # Buttons
        buttons_layout = QHBoxLayout()

        self.add_debt_btn = QPushButton("إضافة دين")
        self.add_debt_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.add_debt_btn.clicked.connect(self.add_debt)

        self.edit_debt_btn = QPushButton("تعديل الدين")
        self.edit_debt_btn.setStyleSheet("background-color: #2196F3; color: white;")
        self.edit_debt_btn.clicked.connect(self.edit_debt)

        self.pay_debt_btn = QPushButton("دفع الدين")
        self.pay_debt_btn.setStyleSheet("background-color: #F44336; color: white;")
        self.pay_debt_btn.clicked.connect(self.pay_debt)

        self.save_debt_btn = QPushButton("حفظ الدين")
        self.save_debt_btn.setStyleSheet("background-color: #9C27B0; color: white;")
        self.save_debt_btn.clicked.connect(self.save_debt)

        buttons_layout.addWidget(self.add_debt_btn)
        buttons_layout.addWidget(self.edit_debt_btn)
        buttons_layout.addWidget(self.pay_debt_btn)
        buttons_layout.addWidget(self.save_debt_btn)

        # Add layouts to main layout
        layout.addLayout(search_layout)
        layout.addWidget(self.debt_customer_list)
        layout.addLayout(form_layout)
        layout.addLayout(buttons_layout)

    # Customer methods
    def search_customers(self):
        """Search customers by name or phone."""
        pass

    def on_customer_selected(self, current, previous):
        """Handle customer selection from list."""
        pass

    def add_customer(self):
        """Add a new customer."""
        pass

    def edit_customer(self):
        """Edit the selected customer."""
        pass

    def delete_customer(self):
        """Delete the selected customer."""
        pass

    def save_customer(self):
        """Save customer information."""
        pass

    # Order methods
    def search_customers_for_order(self):
        """Search customers for order."""
        pass

    def on_order_customer_selected(self, current, previous):
        """Handle customer selection from list for orders."""
        pass

    def on_order_selected(self, current, previous):
        """Handle order selection from list."""
        pass

    def refresh_order_statistics(self):
        """Refresh the daily and monthly order statistics."""
        pass

    def load_customer_orders(self, customer_id):
        """Load orders for a specific customer."""
        pass

    def calculate_remaining(self):
        """Calculate remaining amount for order."""
        pass

    def add_order(self):
        """Add a new order."""
        pass

    def edit_order(self):
        """Edit the selected order."""
        pass

    def delete_order(self):
        """Delete the selected order."""
        pass

    def save_order(self):
        """Save order information."""
        pass

    # Debt methods
    def search_customers_with_debt(self):
        """Search customers with debt."""
        pass

    def on_debt_customer_selected(self, current, previous):
        """Handle debt customer selection from list."""
        pass

    def calculate_debt_remaining(self):
        """Calculate remaining debt."""
        pass

    def add_debt(self):
        """Add a new debt record."""
        pass

    def edit_debt(self):
        """Edit the selected debt record."""
        pass

    def pay_debt(self):
        """Pay debt for the selected customer."""
        pass

    def save_debt(self):
        """Save debt information."""
        pass

    def go_back(self):
        """Handle back button click to return to the previous screen."""
        # This will be implemented in the subclasses
        pass
