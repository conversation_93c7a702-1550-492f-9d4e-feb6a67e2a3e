#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - الواجهة الأساسية
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QFrame, QSizePolicy, QSpacerItem, QLineEdit,
    QListWidget, QGridLayout, QComboBox, QScrollArea, QTabWidget
)
from PySide6.QtCore import Qt, Signal, Slot, QSize
from PySide6.QtGui import QFont, QColor, QPalette, QIcon

from database import Database

class BaseInterface(QMainWindow):
    def __init__(self, role="مستخدم"):
        super().__init__()
        self.role = role
        self.setWindowTitle(f"معاريس الجهراء - {role}")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)

        # Initialize database
        self.db = Database()

        # Center the window
        self.center_on_screen()

        # Setup UI
        self.setup_ui()

    def center_on_screen(self):
        """Center the window on the screen."""
        from PySide6.QtWidgets import QApplication

        screen_geometry = QApplication.primaryScreen().geometry()
        x = (screen_geometry.width() - self.width()) // 2
        y = (screen_geometry.height() - self.height()) // 2

        self.move(x, y)

    def setup_ui(self):
        """Setup the UI components."""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Set main application stylesheet with enhanced colors and fonts
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
            QWidget {
                font-family: "Traditional Arabic", "Arial", "Tahoma";
                font-size: 16px;
                color: #2c3e50;
            }
            QLabel {
                font-size: 16px;
                color: #34495e;
                font-weight: bold;
            }
            QLineEdit {
                font-size: 18px;
                padding: 12px 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
                text-align: right;
                direction: rtl;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QListWidget {
                font-size: 18px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 25px;
                text-align: right;
                direction: rtl;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #e8f4fd;
            }
            QComboBox {
                font-size: 18px;
                padding: 12px 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
                text-align: right;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
        """)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header with logo
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setStyleSheet("""
            #headerFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #667eea, stop: 0.5 #764ba2, stop: 1 #f093fb);
                border-radius: 15px;
                padding: 15px;
                border: 3px solid #4c63d2;
            }
        """)
        header_frame.setMaximumHeight(100)

        header_layout = QHBoxLayout(header_frame)

        # Back button
        self.back_button = QPushButton("رجوع")
        self.back_button.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        self.back_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #e74c3c, stop: 1 #c0392b);
                color: white;
                border-radius: 10px;
                padding: 10px 15px;
                border: 2px solid #a93226;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ec7063, stop: 1 #e74c3c);
                border-color: #c0392b;
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #c0392b, stop: 1 #a93226);
            }
        """)
        self.back_button.setFixedSize(100, 50)
        self.back_button.clicked.connect(self.go_back)

        logo_label = QLabel("معاريس الجهراء")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_font = QFont("Traditional Arabic", 32, QFont.Bold)
        logo_label.setFont(logo_font)
        logo_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                font-weight: bold;
            }
        """)

        role_label = QLabel(f"الدور: {self.role}")
        role_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        role_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        role_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255,255,255,0.2);
                border-radius: 8px;
                padding: 8px 12px;
                font-weight: bold;
            }
        """)

        header_layout.addWidget(self.back_button)
        header_layout.addWidget(logo_label)
        header_layout.addWidget(role_label)

        # Add header to main layout
        main_layout.addWidget(header_frame)

        # Create tab widget for the three main sections
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #3498db;
                border-radius: 15px;
                padding: 10px;
                background-color: white;
            }
            QTabBar::tab {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ecf0f1, stop: 1 #bdc3c7);
                border: 2px solid #95a5a6;
                border-bottom-color: transparent;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                padding: 12px 20px;
                margin-right: 3px;
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border-color: #2980b9;
                border-bottom-color: white;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #5dade2, stop: 1 #3498db);
                color: white;
            }
        """)

        # Create the three tabs
        self.customers_tab = QWidget()
        self.orders_tab = QWidget()
        self.debts_tab = QWidget()

        # Setup each tab
        self.setup_customers_tab()
        self.setup_orders_tab()
        self.setup_debts_tab()

        # Add tabs to tab widget
        self.tab_widget.addTab(self.customers_tab, "العملاء")
        self.tab_widget.addTab(self.orders_tab, "الطلبات")
        self.tab_widget.addTab(self.debts_tab, "الديون")

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

    def setup_customers_tab(self):
        """Setup the customers tab."""
        # Main layout for customers tab
        layout = QHBoxLayout(self.customers_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # Left panel - Customer list
        left_panel = QFrame()
        left_panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border: 2px solid #3498db;
                border-radius: 15px;
                padding: 10px;
            }
        """)
        left_panel.setMaximumWidth(350)

        left_layout = QVBoxLayout(left_panel)

        # Search bar
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        search_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        search_label.setStyleSheet("color: #2c3e50;")

        self.customer_search = QLineEdit()
        self.customer_search.setPlaceholderText("بحث بالاسم أو رقم الهاتف")
        self.customer_search.setFont(QFont("Traditional Arabic", 16))
        self.customer_search.textChanged.connect(self.search_customers)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.customer_search)

        # Customer list
        customers_label = QLabel("العملاء:")
        customers_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        customers_label.setStyleSheet("color: #2c3e50; margin-top: 10px;")

        self.customer_list = QListWidget()
        self.customer_list.setFont(QFont("Traditional Arabic", 16))
        self.customer_list.currentItemChanged.connect(self.on_customer_selected)

        # Add widgets to left layout
        left_layout.addLayout(search_layout)
        left_layout.addWidget(customers_label)
        left_layout.addWidget(self.customer_list)

        # Right panel - Customer details
        right_panel = QFrame()
        right_panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #27ae60;
                border-radius: 15px;
                padding: 15px;
            }
        """)

        right_layout = QVBoxLayout(right_panel)

        # Customer details form
        form_layout = QGridLayout()
        form_layout.setSpacing(15)

        # Name and phone
        name_label = QLabel("الاسم:")
        name_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        name_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px;
            }
        """)
        form_layout.addWidget(name_label, 0, 0)

        self.customer_name = QLineEdit()
        self.customer_name.setFont(QFont("Traditional Arabic", 18))
        self.customer_name.setAlignment(Qt.AlignRight)
        self.customer_name.setStyleSheet("""
            QLineEdit {
                font-size: 18px;
                padding: 12px 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #27ae60;
                background-color: #f8f9fa;
            }
        """)
        form_layout.addWidget(self.customer_name, 0, 1)

        phone_label = QLabel("الهاتف:")
        phone_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        phone_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        phone_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px;
            }
        """)
        form_layout.addWidget(phone_label, 1, 0)

        self.customer_phone = QLineEdit()
        self.customer_phone.setFont(QFont("Traditional Arabic", 18))
        self.customer_phone.setAlignment(Qt.AlignRight)
        self.customer_phone.setStyleSheet("""
            QLineEdit {
                font-size: 18px;
                padding: 12px 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #27ae60;
                background-color: #f8f9fa;
            }
        """)
        form_layout.addWidget(self.customer_phone, 1, 1)

        # Measurements
        measurements_label = QLabel("القياسات:")
        measurements_label.setFont(QFont("Traditional Arabic", 20, QFont.Bold))
        measurements_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                background-color: rgba(39, 174, 96, 0.1);
                border-radius: 8px;
                padding: 8px;
                margin: 10px 0px;
            }
        """)
        form_layout.addWidget(measurements_label, 2, 0, 1, 2)

        # Measurement fields with proper styling
        measurements = [
            ("الصدر:", "customer_chest"),
            ("الكتف:", "customer_shoulder"),
            ("اليد:", "customer_hand"),
            ("الخصر:", "customer_waist"),
            ("الورك:", "customer_hip"),
            ("طول الساق:", "customer_inseam"),
            ("الرقبة:", "customer_neck"),
            ("الطول:", "customer_height")
        ]

        for i, (label_text, field_name) in enumerate(measurements):
            # Create label with proper font and styling
            label = QLabel(label_text)
            label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
            label.setStyleSheet("""
                QLabel {
                    color: #2c3e50;
                    font-weight: bold;
                    padding: 5px;
                    font-size: 18px;
                }
            """)

            # Create input field with proper styling
            field = QLineEdit()
            field.setFont(QFont("Arial", 18))
            field.setAlignment(Qt.AlignCenter)
            field.setStyleSheet("""
                QLineEdit {
                    font-size: 18px;
                    padding: 12px 15px;
                    border: 2px solid #bdc3c7;
                    border-radius: 8px;
                    background-color: white;
                    min-height: 20px;
                }
                QLineEdit:focus {
                    border-color: #27ae60;
                    background-color: #f8f9fa;
                }
            """)

            # Set the field as an attribute
            setattr(self, field_name, field)

            # Add to layout
            form_layout.addWidget(label, i + 3, 0)
            form_layout.addWidget(field, i + 3, 1)

        # Buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # Enhanced button styling
        button_style = """
            QPushButton {{
                font-family: "Traditional Arabic", "Arial";
                font-size: 16px;
                font-weight: bold;
                padding: 12px 20px;
                border-radius: 10px;
                border: 2px solid {border_color};
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 {color1}, stop: 1 {color2});
                color: white;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 {hover_color1}, stop: 1 {hover_color2});
                border-color: {hover_border};
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 {press_color1}, stop: 1 {press_color2});
            }}
        """

        self.add_customer_btn = QPushButton("إضافة عميل")
        self.add_customer_btn.setStyleSheet(button_style.format(
            color1="#27ae60", color2="#229954", border_color="#1e8449",
            hover_color1="#2ecc71", hover_color2="#27ae60", hover_border="#229954",
            press_color1="#229954", press_color2="#1e8449"
        ))
        self.add_customer_btn.clicked.connect(self.add_customer)

        self.edit_customer_btn = QPushButton("تعديل")
        self.edit_customer_btn.setStyleSheet(button_style.format(
            color1="#3498db", color2="#2980b9", border_color="#2471a3",
            hover_color1="#5dade2", hover_color2="#3498db", hover_border="#2980b9",
            press_color1="#2980b9", press_color2="#2471a3"
        ))
        self.edit_customer_btn.clicked.connect(self.edit_customer)

        self.delete_customer_btn = QPushButton("حذف")
        self.delete_customer_btn.setStyleSheet(button_style.format(
            color1="#e74c3c", color2="#c0392b", border_color="#a93226",
            hover_color1="#ec7063", hover_color2="#e74c3c", hover_border="#c0392b",
            press_color1="#c0392b", press_color2="#a93226"
        ))
        self.delete_customer_btn.clicked.connect(self.delete_customer)

        self.save_customer_btn = QPushButton("حفظ")
        self.save_customer_btn.setStyleSheet(button_style.format(
            color1="#9b59b6", color2="#8e44ad", border_color="#7d3c98",
            hover_color1="#bb8fce", hover_color2="#9b59b6", hover_border="#8e44ad",
            press_color1="#8e44ad", press_color2="#7d3c98"
        ))
        self.save_customer_btn.clicked.connect(self.save_customer)

        buttons_layout.addWidget(self.add_customer_btn)
        buttons_layout.addWidget(self.edit_customer_btn)
        buttons_layout.addWidget(self.delete_customer_btn)
        buttons_layout.addWidget(self.save_customer_btn)

        # Add layouts to right panel
        right_layout.addLayout(form_layout)
        right_layout.addLayout(buttons_layout)

        # Add panels to main layout
        layout.addWidget(left_panel)
        layout.addWidget(right_panel)

    def setup_orders_tab(self):
        """Setup the orders tab."""
        # Main layout for orders tab
        layout = QHBoxLayout(self.orders_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # Left panel - Customer list and statistics
        left_panel = QFrame()
        left_panel.setFrameShape(QFrame.StyledPanel)
        left_panel.setMaximumWidth(300)

        left_layout = QVBoxLayout(left_panel)

        # Statistics section
        stats_group = QFrame()
        stats_group.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #f39c12, stop: 1 #e67e22);
                border-radius: 15px;
                padding: 15px;
                border: 3px solid #d68910;
            }
        """)

        stats_layout = QVBoxLayout(stats_group)

        # Daily statistics
        daily_label = QLabel("إحصائيات اليوم:")
        daily_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        daily_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255,255,255,0.2);
                border-radius: 8px;
                padding: 8px;
                margin-bottom: 10px;
            }
        """)
        stats_layout.addWidget(daily_label)

        daily_grid = QGridLayout()
        daily_grid.setSpacing(8)

        # Daily stats labels with enhanced styling
        daily_total_lbl = QLabel("إجمالي المبيعات:")
        daily_total_lbl.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        daily_total_lbl.setStyleSheet("color: white;")
        daily_grid.addWidget(daily_total_lbl, 0, 0)

        self.daily_total_label = QLabel("330.00")
        self.daily_total_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.daily_total_label.setAlignment(Qt.AlignCenter)
        self.daily_total_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: white;
                border-radius: 8px;
                padding: 20px;
                font-size: 24px;
                font-weight: bold;
                min-height: 40px;
                min-width: 120px;
            }
        """)
        daily_grid.addWidget(self.daily_total_label, 0, 1)

        daily_paid_lbl = QLabel("المدفوع:")
        daily_paid_lbl.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        daily_paid_lbl.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        daily_paid_lbl.setStyleSheet("color: white; padding: 5px;")
        daily_grid.addWidget(daily_paid_lbl, 1, 0)

        self.daily_paid_label = QLabel("200.00")
        self.daily_paid_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.daily_paid_label.setAlignment(Qt.AlignCenter)
        self.daily_paid_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                background-color: white;
                border-radius: 8px;
                padding: 20px;
                font-size: 24px;
                font-weight: bold;
                min-height: 40px;
                min-width: 120px;
            }
        """)
        daily_grid.addWidget(self.daily_paid_label, 1, 1)

        daily_remaining_lbl = QLabel("المتبقي:")
        daily_remaining_lbl.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        daily_remaining_lbl.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        daily_remaining_lbl.setStyleSheet("color: white; padding: 5px;")
        daily_grid.addWidget(daily_remaining_lbl, 2, 0)

        self.daily_remaining_label = QLabel("130.00")
        self.daily_remaining_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.daily_remaining_label.setAlignment(Qt.AlignCenter)
        self.daily_remaining_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                background-color: white;
                border-radius: 8px;
                padding: 20px;
                font-size: 24px;
                font-weight: bold;
                min-height: 40px;
                min-width: 120px;
            }
        """)
        daily_grid.addWidget(self.daily_remaining_label, 2, 1)

        stats_layout.addLayout(daily_grid)

        # Monthly statistics
        monthly_label = QLabel("إحصائيات الشهر:")
        monthly_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        monthly_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255,255,255,0.2);
                border-radius: 8px;
                padding: 8px;
                margin: 10px 0px;
            }
        """)
        stats_layout.addWidget(monthly_label)

        monthly_grid = QGridLayout()
        monthly_grid.setSpacing(8)

        # Monthly stats labels with enhanced styling
        monthly_total_lbl = QLabel("إجمالي المبيعات:")
        monthly_total_lbl.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        monthly_total_lbl.setStyleSheet("color: white;")
        monthly_grid.addWidget(monthly_total_lbl, 0, 0)

        self.monthly_total_label = QLabel("610.00")
        self.monthly_total_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.monthly_total_label.setAlignment(Qt.AlignCenter)
        self.monthly_total_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background-color: white;
                border-radius: 8px;
                padding: 20px;
                font-size: 24px;
                font-weight: bold;
                min-height: 40px;
                min-width: 120px;
            }
        """)
        monthly_grid.addWidget(self.monthly_total_label, 0, 1)

        monthly_paid_lbl = QLabel("المدفوع:")
        monthly_paid_lbl.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        monthly_paid_lbl.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        monthly_paid_lbl.setStyleSheet("color: white; padding: 5px;")
        monthly_grid.addWidget(monthly_paid_lbl, 1, 0)

        self.monthly_paid_label = QLabel("310.00")
        self.monthly_paid_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.monthly_paid_label.setAlignment(Qt.AlignCenter)
        self.monthly_paid_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                background-color: white;
                border-radius: 8px;
                padding: 20px;
                font-size: 24px;
                font-weight: bold;
                min-height: 40px;
                min-width: 120px;
            }
        """)
        monthly_grid.addWidget(self.monthly_paid_label, 1, 1)

        monthly_remaining_lbl = QLabel("المتبقي:")
        monthly_remaining_lbl.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        monthly_remaining_lbl.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        monthly_remaining_lbl.setStyleSheet("color: white; padding: 5px;")
        monthly_grid.addWidget(monthly_remaining_lbl, 2, 0)

        self.monthly_remaining_label = QLabel("300.00")
        self.monthly_remaining_label.setFont(QFont("Arial", 24, QFont.Bold))
        self.monthly_remaining_label.setAlignment(Qt.AlignCenter)
        self.monthly_remaining_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                background-color: white;
                border-radius: 8px;
                padding: 20px;
                font-size: 24px;
                font-weight: bold;
                min-height: 40px;
                min-width: 120px;
            }
        """)
        monthly_grid.addWidget(self.monthly_remaining_label, 2, 1)

        stats_layout.addLayout(monthly_grid)

        # Add stats group to left layout
        left_layout.addWidget(stats_group)

        # Search bar for customer
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث عن عميل:")
        self.order_customer_search = QLineEdit()
        self.order_customer_search.setPlaceholderText("بحث بالاسم أو رقم الهاتف")
        self.order_customer_search.textChanged.connect(self.search_customers_for_order)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.order_customer_search)

        # Customer list
        self.order_customer_list = QListWidget()
        self.order_customer_list.setFont(QFont("Traditional Arabic", 12))
        self.order_customer_list.currentItemChanged.connect(self.on_order_customer_selected)

        # Add widgets to left layout
        left_layout.addLayout(search_layout)
        left_layout.addWidget(QLabel("العملاء:"))
        left_layout.addWidget(self.order_customer_list)

        # Refresh button for statistics
        self.refresh_stats_btn = QPushButton("تحديث الإحصائيات")
        self.refresh_stats_btn.setStyleSheet("background-color: #607D8B; color: white;")
        self.refresh_stats_btn.clicked.connect(self.refresh_order_statistics)
        left_layout.addWidget(self.refresh_stats_btn)

        # Right panel - Order details and list
        right_panel = QFrame()
        right_panel.setFrameShape(QFrame.StyledPanel)

        right_layout = QVBoxLayout(right_panel)

        # Order form
        form_group = QFrame()
        form_group.setFrameShape(QFrame.StyledPanel)
        form_layout = QGridLayout(form_group)

        # Customer info
        customer_label = QLabel("العميل:")
        customer_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        customer_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(customer_label, 0, 0)

        self.order_customer_name = QLabel("لم يتم اختيار عميل")
        self.order_customer_name.setFont(QFont("Traditional Arabic", 16))
        self.order_customer_name.setStyleSheet("color: #7f8c8d; padding: 5px;")
        form_layout.addWidget(self.order_customer_name, 0, 1)

        # Order type
        order_type_label = QLabel("نوع الطلب:")
        order_type_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        order_type_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(order_type_label, 1, 0)

        self.order_type = QComboBox()
        self.order_type.addItems(["صيفي", "شتوي"])
        form_layout.addWidget(self.order_type, 1, 1)

        # Total price
        total_price_label = QLabel("السعر الإجمالي:")
        total_price_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        total_price_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(total_price_label, 2, 0)

        self.order_total_price = QLineEdit()
        form_layout.addWidget(self.order_total_price, 2, 1)

        # Payment
        payment_label = QLabel("المدفوع:")
        payment_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        payment_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(payment_label, 3, 0)

        self.order_payment = QLineEdit()
        self.order_payment.textChanged.connect(self.calculate_remaining)
        form_layout.addWidget(self.order_payment, 3, 1)

        # Remaining
        remaining_label = QLabel("المتبقي:")
        remaining_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        remaining_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(remaining_label, 4, 0)

        self.order_remaining = QLabel("0")
        self.order_remaining.setFont(QFont("Traditional Arabic", 16))
        self.order_remaining.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 5px;")
        form_layout.addWidget(self.order_remaining, 4, 1)

        # Order ID (hidden)
        self.current_order_id = None

        # Buttons
        buttons_layout = QHBoxLayout()

        self.add_order_btn = QPushButton("إضافة طلب")
        self.add_order_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.add_order_btn.clicked.connect(self.add_order)

        self.edit_order_btn = QPushButton("تعديل الطلب")
        self.edit_order_btn.setStyleSheet("background-color: #2196F3; color: white;")
        self.edit_order_btn.clicked.connect(self.edit_order)

        self.delete_order_btn = QPushButton("حذف الطلب")
        self.delete_order_btn.setStyleSheet("background-color: #F44336; color: white;")
        self.delete_order_btn.clicked.connect(self.delete_order)

        self.save_order_btn = QPushButton("حفظ الطلب")
        self.save_order_btn.setStyleSheet("background-color: #9C27B0; color: white;")
        self.save_order_btn.clicked.connect(self.save_order)

        buttons_layout.addWidget(self.add_order_btn)
        buttons_layout.addWidget(self.edit_order_btn)
        buttons_layout.addWidget(self.delete_order_btn)
        buttons_layout.addWidget(self.save_order_btn)

        # Add form and buttons to right layout
        right_layout.addWidget(form_group)
        right_layout.addLayout(buttons_layout)

        # Orders list
        orders_label = QLabel("طلبات العميل:")
        orders_label.setFont(QFont("Traditional Arabic", 14, QFont.Bold))
        right_layout.addWidget(orders_label)

        self.orders_list = QListWidget()
        self.orders_list.setFont(QFont("Traditional Arabic", 12))
        self.orders_list.currentItemChanged.connect(self.on_order_selected)
        right_layout.addWidget(self.orders_list)

        # Add panels to main layout
        layout.addWidget(left_panel)
        layout.addWidget(right_panel)

    def setup_debts_tab(self):
        """Setup the debts tab."""
        # Main layout for debts tab
        layout = QVBoxLayout(self.debts_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # Search bar for customer
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث عن عميل:")
        self.debt_customer_search = QLineEdit()
        self.debt_customer_search.setPlaceholderText("بحث بالاسم أو رقم الهاتف")
        self.debt_customer_search.textChanged.connect(self.search_customers_with_debt)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.debt_customer_search)

        # Customer list
        self.debt_customer_list = QListWidget()
        self.debt_customer_list.setFont(QFont("Traditional Arabic", 12))
        self.debt_customer_list.currentItemChanged.connect(self.on_debt_customer_selected)

        # Debt form
        form_layout = QGridLayout()

        # Customer info
        debt_customer_label = QLabel("العميل:")
        debt_customer_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        debt_customer_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(debt_customer_label, 0, 0)

        self.debt_customer_name = QLabel("لم يتم اختيار عميل")
        self.debt_customer_name.setFont(QFont("Traditional Arabic", 16))
        self.debt_customer_name.setStyleSheet("color: #7f8c8d; padding: 5px;")
        form_layout.addWidget(self.debt_customer_name, 0, 1)

        # Total due
        total_due_label = QLabel("إجمالي المستحق:")
        total_due_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        total_due_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(total_due_label, 1, 0)

        self.debt_total_due = QLineEdit()
        form_layout.addWidget(self.debt_total_due, 1, 1)

        # Paid
        debt_paid_label = QLabel("المدفوع:")
        debt_paid_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        debt_paid_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(debt_paid_label, 2, 0)

        self.debt_paid = QLineEdit()
        self.debt_paid.textChanged.connect(self.calculate_debt_remaining)
        form_layout.addWidget(self.debt_paid, 2, 1)

        # Remaining
        debt_remaining_label = QLabel("المتبقي:")
        debt_remaining_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        debt_remaining_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(debt_remaining_label, 3, 0)

        self.debt_remaining = QLabel("0")
        self.debt_remaining.setFont(QFont("Traditional Arabic", 16))
        self.debt_remaining.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 5px;")
        form_layout.addWidget(self.debt_remaining, 3, 1)

        # Buttons
        buttons_layout = QHBoxLayout()

        self.add_debt_btn = QPushButton("إضافة دين")
        self.add_debt_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.add_debt_btn.clicked.connect(self.add_debt)

        self.edit_debt_btn = QPushButton("تعديل الدين")
        self.edit_debt_btn.setStyleSheet("background-color: #2196F3; color: white;")
        self.edit_debt_btn.clicked.connect(self.edit_debt)

        self.pay_debt_btn = QPushButton("دفع الدين")
        self.pay_debt_btn.setStyleSheet("background-color: #F44336; color: white;")
        self.pay_debt_btn.clicked.connect(self.pay_debt)

        self.save_debt_btn = QPushButton("حفظ الدين")
        self.save_debt_btn.setStyleSheet("background-color: #9C27B0; color: white;")
        self.save_debt_btn.clicked.connect(self.save_debt)

        buttons_layout.addWidget(self.add_debt_btn)
        buttons_layout.addWidget(self.edit_debt_btn)
        buttons_layout.addWidget(self.pay_debt_btn)
        buttons_layout.addWidget(self.save_debt_btn)

        # Add layouts to main layout
        layout.addLayout(search_layout)
        layout.addWidget(self.debt_customer_list)
        layout.addLayout(form_layout)
        layout.addLayout(buttons_layout)

    # Customer methods
    def search_customers(self):
        """Search customers by name or phone."""
        pass

    def on_customer_selected(self, current, previous):
        """Handle customer selection from list."""
        pass

    def add_customer(self):
        """Add a new customer."""
        pass

    def edit_customer(self):
        """Edit the selected customer."""
        pass

    def delete_customer(self):
        """Delete the selected customer."""
        pass

    def save_customer(self):
        """Save customer information."""
        pass

    # Order methods
    def search_customers_for_order(self):
        """Search customers for order."""
        pass

    def on_order_customer_selected(self, current, previous):
        """Handle customer selection from list for orders."""
        pass

    def on_order_selected(self, current, previous):
        """Handle order selection from list."""
        pass

    def refresh_order_statistics(self):
        """Refresh the daily and monthly order statistics."""
        pass

    def load_customer_orders(self, customer_id):
        """Load orders for a specific customer."""
        pass

    def calculate_remaining(self):
        """Calculate remaining amount for order."""
        pass

    def add_order(self):
        """Add a new order."""
        pass

    def edit_order(self):
        """Edit the selected order."""
        pass

    def delete_order(self):
        """Delete the selected order."""
        pass

    def save_order(self):
        """Save order information."""
        pass

    # Debt methods
    def search_customers_with_debt(self):
        """Search customers with debt."""
        pass

    def on_debt_customer_selected(self, current, previous):
        """Handle debt customer selection from list."""
        pass

    def calculate_debt_remaining(self):
        """Calculate remaining debt."""
        pass

    def add_debt(self):
        """Add a new debt record."""
        pass

    def edit_debt(self):
        """Edit the selected debt record."""
        pass

    def pay_debt(self):
        """Pay debt for the selected customer."""
        pass

    def save_debt(self):
        """Save debt information."""
        pass

    def go_back(self):
        """Handle back button click to return to the previous screen."""
        # This will be implemented in the subclasses
        pass
