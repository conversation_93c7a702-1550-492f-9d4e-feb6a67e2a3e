#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - الواجهة الأساسية
"""

# First, try to import PySide6
try:
    from PySide6.QtWidgets import (
        QMain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
        QPushButton, QFrame, QLineEdit, QListWidget, QGridLayout,
        QComboBox, QTabWidget, QMessageBox, QApplication
    )
    from PySide6.QtCore import Qt
    from PySide6.QtGui import QFont
    PYSIDE6_AVAILABLE = True
except ImportError:
    # If PySide6 fails, try PyQt6 as an alternative
    try:
        from PyQt6.QtWidgets import (
            QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel,
            QPushButton, QFrame, QLineEdit, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON>ridLayout,
            <PERSON><PERSON>omb<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Q<PERSON><PERSON>ageBox, QApp<PERSON>
        )
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        PYSIDE6_AVAILABLE = True
    except ImportError:
        # Fallback classes for when neither PySide6 nor PyQt6 is available
        class QMainWindow: pass
        class QWidget: pass
        class QVBoxLayout: pass
        class QHBoxLayout: pass
        class QLabel: pass
        class QPushButton: pass
        class QFrame: pass
        class QLineEdit: pass
        class QListWidget: pass
        class QGridLayout: pass
        class QComboBox: pass
        class QTabWidget: pass
        class QMessageBox: pass
        class Qt: 
            AlignCenter = None
            AlignRight = None
            AlignVCenter = None
            UserRole = None
        class QFont:
            Bold = None
        PYSIDE6_AVAILABLE = False

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import Database

class BaseInterface(QMainWindow):
    def __init__(self, role="مستخدم"):
        super().__init__()
        self.role = role
        self.setWindowTitle(f"معاريس الجهراء - {role}")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)

        # Initialize database
        self.db = Database()

        # Center the window
        self.center_on_screen()

        # Setup UI
        self.setup_ui()

    def center_on_screen(self):
        """Center the window on the screen."""
        try:
            screen_geometry = QApplication.primaryScreen().geometry()
            x = (screen_geometry.width() - self.width()) // 2
            y = (screen_geometry.height() - self.height()) // 2
            self.move(x, y)
        except (ImportError, AttributeError):
            # Fallback if PySide6 is not available or QApplication not initialized
            pass

    def setup_ui(self):
        """Setup the UI components."""
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Set main application stylesheet with enhanced colors and fonts
        self.setStyleSheet("""
            QMainWindow {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #f8f9fa, stop: 1 #e9ecef);
            }
            QWidget {
                font-family: "Traditional Arabic", "Arial", "Tahoma";
                font-size: 16px;
                color: #2c3e50;
            }
            QLabel {
                font-size: 16px;
                color: #34495e;
                font-weight: bold;
            }
            QLineEdit {
                font-size: 18px;
                padding: 12px 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
                text-align: right;
                direction: rtl;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
            QListWidget {
                font-size: 18px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #ecf0f1;
                min-height: 25px;
                text-align: right;
                direction: rtl;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #e8f4fd;
            }
            QComboBox {
                font-size: 18px;
                padding: 12px 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
                text-align: right;
            }
            QComboBox:focus {
                border-color: #3498db;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }
        """)

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)

        # Header with logo
        header_frame = QFrame()
        header_frame.setObjectName("headerFrame")
        header_frame.setStyleSheet("""
            #headerFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #f39c12, stop: 0.5 #e67e22, stop: 1 #d68910);
                border-radius: 15px;
                padding: 15px;
                border: 3px solid #d68910;
            }
        """)
        header_frame.setMaximumHeight(100)

        header_layout = QHBoxLayout(header_frame)

        # Back button
        self.back_button = QPushButton("رجوع")
        self.back_button.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        self.back_button.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #e74c3c, stop: 1 #c0392b);
                color: white;
                border-radius: 10px;
                padding: 10px 15px;
                border: 2px solid #a93226;
                font-weight: bold;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ec7063, stop: 1 #e74c3c);
                border-color: #c0392b;
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #c0392b, stop: 1 #a93226);
            }
        """)
        self.back_button.setFixedSize(100, 50)
        self.back_button.clicked.connect(self.go_back)

        logo_label = QLabel("معاريس الجهراء")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_font = QFont("Traditional Arabic", 32, QFont.Bold)
        logo_label.setFont(logo_font)
        logo_label.setStyleSheet("""
            QLabel {
                color: white;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
                font-weight: bold;
            }
        """)

        role_label = QLabel(f"الدور: {self.role}")
        role_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        role_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        role_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255,255,255,0.2);
                border-radius: 8px;
                padding: 8px 12px;
                font-weight: bold;
            }
        """)

        header_layout.addWidget(self.back_button)
        header_layout.addWidget(logo_label)
        header_layout.addWidget(role_label)

        # Add header to main layout
        main_layout.addWidget(header_frame)

        # Create tab widget for the three main sections
        self.tab_widget = QTabWidget()
        self.tab_widget.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #3498db;
                border-radius: 15px;
                padding: 10px;
                background-color: white;
            }
            QTabBar::tab {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #ecf0f1, stop: 1 #bdc3c7);
                border: 2px solid #95a5a6;
                border-bottom-color: transparent;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                padding: 12px 20px;
                margin-right: 3px;
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                min-width: 120px;
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #3498db, stop: 1 #2980b9);
                color: white;
                border-color: #2980b9;
                border-bottom-color: white;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 #5dade2, stop: 1 #3498db);
                color: white;
            }
        """)

        # Create the four tabs
        self.customers_tab = QWidget()
        self.orders_tab = QWidget()
        self.debts_tab = QWidget()
        self.reports_tab = QWidget()

        # Setup each tab
        self.setup_customers_tab()
        self.setup_orders_tab()
        self.setup_debts_tab()
        self.setup_reports_tab()

        # Add tabs to tab widget
        self.tab_widget.addTab(self.customers_tab, "العملاء")
        self.tab_widget.addTab(self.orders_tab, "الطلبات")
        self.tab_widget.addTab(self.debts_tab, "الديون")
        self.tab_widget.addTab(self.reports_tab, "التقارير")

        # Add tab widget to main layout
        main_layout.addWidget(self.tab_widget)

    def setup_customers_tab(self):
        """Setup the customers tab."""
        # Main layout for customers tab
        layout = QHBoxLayout(self.customers_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # Left panel - Customer list
        left_panel = QFrame()
        left_panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #f8f9fa, stop: 1 #e9ecef);
                border: 2px solid #3498db;
                border-radius: 15px;
                padding: 10px;
            }
        """)
        left_panel.setMaximumWidth(350)

        left_layout = QVBoxLayout(left_panel)

        # Search bar
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث:")
        search_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        search_label.setStyleSheet("color: #2c3e50;")

        self.customer_search = QLineEdit()
        self.customer_search.setPlaceholderText("بحث بالاسم أو رقم الهاتف")
        self.customer_search.setFont(QFont("Traditional Arabic", 16))
        self.customer_search.textChanged.connect(self.search_customers)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.customer_search)

        # Customer list
        customers_label = QLabel("العملاء:")
        customers_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        customers_label.setStyleSheet("color: #2c3e50; margin-top: 10px;")

        self.customer_list = QListWidget()
        self.customer_list.setFont(QFont("Traditional Arabic", 16))
        self.customer_list.currentItemChanged.connect(self.on_customer_selected)

        # Add widgets to left layout
        left_layout.addLayout(search_layout)
        left_layout.addWidget(customers_label)
        left_layout.addWidget(self.customer_list)

        # Right panel - Customer details
        right_panel = QFrame()
        right_panel.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 2px solid #3498db;
                border-radius: 15px;
                padding: 15px;
            }
        """)

        right_layout = QVBoxLayout(right_panel)

        # Customer details form
        form_layout = QGridLayout()
        form_layout.setSpacing(15)

        # Name and phone
        name_label = QLabel("الاسم:")
        name_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        name_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px;
            }
        """)
        form_layout.addWidget(name_label, 0, 0)

        self.customer_name = QLineEdit()
        self.customer_name.setFont(QFont("Traditional Arabic", 18))
        self.customer_name.setAlignment(Qt.AlignRight)
        self.customer_name.setStyleSheet("""
            QLineEdit {
                font-size: 18px;
                padding: 12px 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """)
        form_layout.addWidget(self.customer_name, 0, 1)

        phone_label = QLabel("الهاتف:")
        phone_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        phone_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        phone_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                font-weight: bold;
                padding: 5px;
            }
        """)
        form_layout.addWidget(phone_label, 1, 0)

        self.customer_phone = QLineEdit()
        self.customer_phone.setFont(QFont("Traditional Arabic", 18))
        self.customer_phone.setAlignment(Qt.AlignRight)
        self.customer_phone.setStyleSheet("""
            QLineEdit {
                font-size: 18px;
                padding: 12px 15px;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                background-color: white;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #3498db;
                background-color: #f8f9fa;
            }
        """)
        form_layout.addWidget(self.customer_phone, 1, 1)

        # Measurements
        measurements_label = QLabel("القياسات:")
        measurements_label.setFont(QFont("Traditional Arabic", 20, QFont.Bold))
        measurements_label.setStyleSheet("""
            QLabel {
                color: #3498db;
                background-color: rgba(52, 152, 219, 0.1);
                border-radius: 8px;
                padding: 8px;
                margin: 10px 0px;
            }
        """)
        form_layout.addWidget(measurements_label, 2, 0, 1, 2)

        # Measurement fields with proper styling
        measurements = [
            ("الصدر:", "customer_chest"),
            ("الكتف:", "customer_shoulder"),
            ("اليد:", "customer_hand"),
            ("الخصر:", "customer_waist"),
            ("الورك:", "customer_hip"),
            ("طول الساق:", "customer_inseam"),
            ("الرقبة:", "customer_neck"),
            ("الطول:", "customer_height")
        ]

        for i, (label_text, field_name) in enumerate(measurements):
            # Create label with proper font and styling
            label = QLabel(label_text)
            label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
            label.setStyleSheet("""
                QLabel {
                    color: #2c3e50;
                    font-weight: bold;
                    padding: 5px;
                    font-size: 18px;
                }
            """)

            # Create input field with proper styling
            field = QLineEdit()
            field.setFont(QFont("Arial", 18))
            field.setAlignment(Qt.AlignCenter)
            field.setStyleSheet("""
                QLineEdit {
                    font-size: 18px;
                    padding: 12px 15px;
                    border: 2px solid #bdc3c7;
                    border-radius: 8px;
                    background-color: white;
                    min-height: 20px;
                }
                QLineEdit:focus {
                    border-color: #3498db;
                    background-color: #f8f9fa;
                }
            """)

            # Set the field as an attribute
            setattr(self, field_name, field)

            # Add to layout
            form_layout.addWidget(label, i + 3, 0)
            form_layout.addWidget(field, i + 3, 1)

        # Buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # Enhanced button styling
        button_style = """
            QPushButton {{
                font-family: "Traditional Arabic", "Arial";
                font-size: 16px;
                font-weight: bold;
                padding: 12px 20px;
                border-radius: 10px;
                border: 2px solid {border_color};
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 {color1}, stop: 1 {color2});
                color: white;
                min-width: 120px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 {hover_color1}, stop: 1 {hover_color2});
                border-color: {hover_border};
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                          stop: 0 {press_color1}, stop: 1 {press_color2});
            }}
        """

        self.add_customer_btn = QPushButton("إضافة عميل")
        self.add_customer_btn.setStyleSheet(button_style.format(
            color1="#3498db", color2="#2980b9", border_color="#2471a3",
            hover_color1="#5dade2", hover_color2="#3498db", hover_border="#2980b9",
            press_color1="#2980b9", press_color2="#2471a3"
        ))
        self.add_customer_btn.clicked.connect(self.add_customer)

        self.edit_customer_btn = QPushButton("تعديل")
        self.edit_customer_btn.setStyleSheet(button_style.format(
            color1="#3498db", color2="#2980b9", border_color="#2471a3",
            hover_color1="#5dade2", hover_color2="#3498db", hover_border="#2980b9",
            press_color1="#2980b9", press_color2="#2471a3"
        ))
        self.edit_customer_btn.clicked.connect(self.edit_customer)

        self.delete_customer_btn = QPushButton("حذف")
        self.delete_customer_btn.setStyleSheet(button_style.format(
            color1="#e74c3c", color2="#c0392b", border_color="#a93226",
            hover_color1="#ec7063", hover_color2="#e74c3c", hover_border="#c0392b",
            press_color1="#c0392b", press_color2="#a93226"
        ))
        self.delete_customer_btn.clicked.connect(self.delete_customer)

        self.save_customer_btn = QPushButton("حفظ")
        self.save_customer_btn.setStyleSheet(button_style.format(
            color1="#f39c12", color2="#e67e22", border_color="#d68910",
            hover_color1="#f4d03f", hover_color2="#f39c12", hover_border="#e67e22",
            press_color1="#e67e22", press_color2="#d68910"
        ))
        self.save_customer_btn.clicked.connect(self.save_customer)

        buttons_layout.addWidget(self.add_customer_btn)
        buttons_layout.addWidget(self.edit_customer_btn)
        buttons_layout.addWidget(self.delete_customer_btn)
        buttons_layout.addWidget(self.save_customer_btn)

        # Add layouts to right panel
        right_layout.addLayout(form_layout)
        right_layout.addLayout(buttons_layout)

        # Add panels to main layout
        layout.addWidget(left_panel)
        layout.addWidget(right_panel)

    def setup_orders_tab(self):
        """Setup the orders tab."""
        # Main layout for orders tab
        layout = QHBoxLayout(self.orders_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # Left panel - Customer list
        left_panel = QFrame()
        left_panel.setFrameShape(QFrame.StyledPanel)
        left_panel.setMaximumWidth(300)

        left_layout = QVBoxLayout(left_panel)

        # Search bar for customer
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث عن عميل:")
        self.order_customer_search = QLineEdit()
        self.order_customer_search.setPlaceholderText("بحث بالاسم أو رقم الهاتف")
        self.order_customer_search.textChanged.connect(self.search_customers_for_order)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.order_customer_search)

        # Customer list
        self.order_customer_list = QListWidget()
        self.order_customer_list.setFont(QFont("Traditional Arabic", 12))
        self.order_customer_list.currentItemChanged.connect(self.on_order_customer_selected)

        # Add widgets to left layout
        left_layout.addLayout(search_layout)
        left_layout.addWidget(QLabel("العملاء:"))
        left_layout.addWidget(self.order_customer_list)

        # Refresh button for statistics
        self.refresh_stats_btn = QPushButton("تحديث الإحصائيات")
        self.refresh_stats_btn.setStyleSheet("background-color: #607D8B; color: white;")
        self.refresh_stats_btn.clicked.connect(self.refresh_order_statistics)
        left_layout.addWidget(self.refresh_stats_btn)

        # Right panel - Order details and list
        right_panel = QFrame()
        right_panel.setFrameShape(QFrame.StyledPanel)

        right_layout = QVBoxLayout(right_panel)

        # Order form
        form_group = QFrame()
        form_group.setFrameShape(QFrame.StyledPanel)
        form_layout = QGridLayout(form_group)

        # Customer info
        customer_label = QLabel("العميل:")
        customer_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        customer_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(customer_label, 0, 0)

        self.order_customer_name = QLabel("لم يتم اختيار عميل")
        self.order_customer_name.setFont(QFont("Traditional Arabic", 16))
        self.order_customer_name.setStyleSheet("color: #7f8c8d; padding: 5px;")
        form_layout.addWidget(self.order_customer_name, 0, 1)

        # Order type
        order_type_label = QLabel("نوع الطلب:")
        order_type_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        order_type_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(order_type_label, 1, 0)

        self.order_type = QComboBox()
        self.order_type.addItems(["صيفي", "شتوي"])
        form_layout.addWidget(self.order_type, 1, 1)

        # Total price
        total_price_label = QLabel("السعر الإجمالي:")
        total_price_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        total_price_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(total_price_label, 2, 0)

        self.order_total_price = QLineEdit()
        form_layout.addWidget(self.order_total_price, 2, 1)

        # Payment
        payment_label = QLabel("المدفوع:")
        payment_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        payment_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(payment_label, 3, 0)

        self.order_payment = QLineEdit()
        self.order_payment.textChanged.connect(self.calculate_remaining)
        form_layout.addWidget(self.order_payment, 3, 1)

        # Remaining
        remaining_label = QLabel("المتبقي:")
        remaining_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        remaining_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(remaining_label, 4, 0)

        self.order_remaining = QLabel("0")
        self.order_remaining.setFont(QFont("Traditional Arabic", 16))
        self.order_remaining.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 5px;")
        form_layout.addWidget(self.order_remaining, 4, 1)

        # Order ID (hidden)
        self.current_order_id = None

        # Buttons
        buttons_layout = QHBoxLayout()

        self.add_order_btn = QPushButton("إضافة طلب")
        self.add_order_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.add_order_btn.clicked.connect(self.add_order)

        self.edit_order_btn = QPushButton("تعديل الطلب")
        self.edit_order_btn.setStyleSheet("background-color: #2196F3; color: white;")
        self.edit_order_btn.clicked.connect(self.edit_order)

        self.delete_order_btn = QPushButton("حذف الطلب")
        self.delete_order_btn.setStyleSheet("background-color: #F44336; color: white;")
        self.delete_order_btn.clicked.connect(self.delete_order)

        self.save_order_btn = QPushButton("حفظ الطلب")
        self.save_order_btn.setStyleSheet("background-color: #f39c12; color: white;")
        self.save_order_btn.clicked.connect(self.save_order)

        buttons_layout.addWidget(self.add_order_btn)
        buttons_layout.addWidget(self.edit_order_btn)
        buttons_layout.addWidget(self.delete_order_btn)
        buttons_layout.addWidget(self.save_order_btn)

        # Add form and buttons to right layout
        right_layout.addWidget(form_group)
        right_layout.addLayout(buttons_layout)

        # Orders list
        orders_label = QLabel("طلبات العميل:")
        orders_label.setFont(QFont("Traditional Arabic", 14, QFont.Bold))
        right_layout.addWidget(orders_label)

        self.orders_list = QListWidget()
        self.orders_list.setFont(QFont("Traditional Arabic", 12))
        self.orders_list.currentItemChanged.connect(self.on_order_selected)
        right_layout.addWidget(self.orders_list)

        # Add panels to main layout
        layout.addWidget(left_panel)
        layout.addWidget(right_panel)

    def setup_debts_tab(self):
        """Setup the debts tab."""
        # Main layout for debts tab
        layout = QVBoxLayout(self.debts_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # Search bar for customer
        search_layout = QHBoxLayout()
        search_label = QLabel("بحث عن عميل:")
        self.debt_customer_search = QLineEdit()
        self.debt_customer_search.setPlaceholderText("بحث بالاسم أو رقم الهاتف")
        self.debt_customer_search.textChanged.connect(self.search_customers_with_debt)

        search_layout.addWidget(search_label)
        search_layout.addWidget(self.debt_customer_search)

        # Customer list
        self.debt_customer_list = QListWidget()
        self.debt_customer_list.setFont(QFont("Traditional Arabic", 12))
        self.debt_customer_list.currentItemChanged.connect(self.on_debt_customer_selected)

        # Debt form
        form_layout = QGridLayout()

        # Customer info
        debt_customer_label = QLabel("العميل:")
        debt_customer_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        debt_customer_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(debt_customer_label, 0, 0)

        self.debt_customer_name = QLabel("لم يتم اختيار عميل")
        self.debt_customer_name.setFont(QFont("Traditional Arabic", 16))
        self.debt_customer_name.setStyleSheet("color: #7f8c8d; padding: 5px;")
        form_layout.addWidget(self.debt_customer_name, 0, 1)

        # Total due
        total_due_label = QLabel("إجمالي المستحق:")
        total_due_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        total_due_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(total_due_label, 1, 0)

        self.debt_total_due = QLineEdit()
        form_layout.addWidget(self.debt_total_due, 1, 1)

        # Paid
        debt_paid_label = QLabel("المدفوع:")
        debt_paid_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        debt_paid_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(debt_paid_label, 2, 0)

        self.debt_paid = QLineEdit()
        self.debt_paid.textChanged.connect(self.calculate_debt_remaining)
        form_layout.addWidget(self.debt_paid, 2, 1)

        # Remaining
        debt_remaining_label = QLabel("المتبقي:")
        debt_remaining_label.setFont(QFont("Traditional Arabic", 18, QFont.Bold))
        debt_remaining_label.setStyleSheet("color: #2c3e50; font-weight: bold; padding: 5px;")
        form_layout.addWidget(debt_remaining_label, 3, 0)

        self.debt_remaining = QLabel("0")
        self.debt_remaining.setFont(QFont("Traditional Arabic", 16))
        self.debt_remaining.setStyleSheet("color: #e74c3c; font-weight: bold; padding: 5px;")
        form_layout.addWidget(self.debt_remaining, 3, 1)

        # Buttons
        buttons_layout = QHBoxLayout()

        self.add_debt_btn = QPushButton("إضافة دين")
        self.add_debt_btn.setStyleSheet("background-color: #4CAF50; color: white;")
        self.add_debt_btn.clicked.connect(self.add_debt)

        self.edit_debt_btn = QPushButton("تعديل الدين")
        self.edit_debt_btn.setStyleSheet("background-color: #2196F3; color: white;")
        self.edit_debt_btn.clicked.connect(self.edit_debt)

        self.pay_debt_btn = QPushButton("دفع الدين")
        self.pay_debt_btn.setStyleSheet("background-color: #F44336; color: white;")
        self.pay_debt_btn.clicked.connect(self.pay_debt)

        self.save_debt_btn = QPushButton("حفظ الدين")
        self.save_debt_btn.setStyleSheet("background-color: #f39c12; color: white;")
        self.save_debt_btn.clicked.connect(self.save_debt)

        buttons_layout.addWidget(self.add_debt_btn)
        buttons_layout.addWidget(self.edit_debt_btn)
        buttons_layout.addWidget(self.pay_debt_btn)
        buttons_layout.addWidget(self.save_debt_btn)

        # Add layouts to main layout
        layout.addLayout(search_layout)
        layout.addWidget(self.debt_customer_list)
        layout.addLayout(form_layout)
        layout.addLayout(buttons_layout)

    def setup_reports_tab(self):
        """Setup the reports tab."""
        # Main layout for reports tab
        layout = QHBoxLayout(self.reports_tab)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(20)

        # Daily Orders Section
        daily_section = QFrame()
        daily_section.setFrameShape(QFrame.StyledPanel)
        daily_section.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #3498db, stop: 1 #2980b9);
                border-radius: 15px;
                padding: 15px;
                border: 3px solid #2471a3;
                margin: 5px;
            }
        """)
        daily_layout = QVBoxLayout(daily_section)

        # Daily title and summary
        daily_title = QLabel("تقرير طلبات اليوم")
        daily_title.setFont(QFont("Traditional Arabic", 20, QFont.Bold))
        daily_title.setAlignment(Qt.AlignCenter)
        daily_title.setStyleSheet("color: white; margin-bottom: 15px;")
        daily_layout.addWidget(daily_title)

        # Daily summary
        daily_summary_layout = QVBoxLayout()

        self.daily_total_label = QLabel("الإجمالي: 330.00 د.ك")
        self.daily_total_label.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        self.daily_total_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255,255,255,0.2);
                border-radius: 8px;
                padding: 12px;
                margin: 5px;
            }
        """)

        self.daily_paid_label = QLabel("المدفوع: 200.00 د.ك")
        self.daily_paid_label.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        self.daily_paid_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255,255,255,0.2);
                border-radius: 8px;
                padding: 12px;
                margin: 5px;
            }
        """)

        self.daily_remaining_label = QLabel("المتبقي: 130.00 د.ك")
        self.daily_remaining_label.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        self.daily_remaining_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255,255,255,0.2);
                border-radius: 8px;
                padding: 12px;
                margin: 5px;
            }
        """)

        daily_summary_layout.addWidget(self.daily_total_label)
        daily_summary_layout.addWidget(self.daily_paid_label)
        daily_summary_layout.addWidget(self.daily_remaining_label)
        daily_layout.addLayout(daily_summary_layout)

        # Daily orders list
        daily_orders_label = QLabel("قائمة طلبات اليوم:")
        daily_orders_label.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        daily_orders_label.setStyleSheet("color: white; margin-top: 15px;")
        daily_layout.addWidget(daily_orders_label)

        self.daily_orders_list = QListWidget()
        self.daily_orders_list.setFont(QFont("Traditional Arabic", 14))
        self.daily_orders_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border-radius: 8px;
                border: 2px solid #2471a3;
                padding: 5px;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #ecf0f1;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        daily_layout.addWidget(self.daily_orders_list)

        layout.addWidget(daily_section)

        # Monthly Orders Section
        monthly_section = QFrame()
        monthly_section.setFrameShape(QFrame.StyledPanel)
        monthly_section.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1: 0, y1: 0, x2: 1, y2: 1,
                                          stop: 0 #e67e22, stop: 1 #d35400);
                border-radius: 15px;
                padding: 15px;
                border: 3px solid #ba4a00;
                margin: 5px;
            }
        """)
        monthly_layout = QVBoxLayout(monthly_section)

        # Monthly title and summary
        monthly_title = QLabel("تقرير طلبات الشهر")
        monthly_title.setFont(QFont("Traditional Arabic", 20, QFont.Bold))
        monthly_title.setAlignment(Qt.AlignCenter)
        monthly_title.setStyleSheet("color: white; margin-bottom: 15px;")
        monthly_layout.addWidget(monthly_title)

        # Monthly summary
        monthly_summary_layout = QVBoxLayout()

        self.monthly_total_label = QLabel("الإجمالي: 610.00 د.ك")
        self.monthly_total_label.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        self.monthly_total_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255,255,255,0.2);
                border-radius: 8px;
                padding: 12px;
                margin: 5px;
            }
        """)

        self.monthly_paid_label = QLabel("المدفوع: 310.00 د.ك")
        self.monthly_paid_label.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        self.monthly_paid_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255,255,255,0.2);
                border-radius: 8px;
                padding: 12px;
                margin: 5px;
            }
        """)

        self.monthly_remaining_label = QLabel("المتبقي: 300.00 د.ك")
        self.monthly_remaining_label.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        self.monthly_remaining_label.setStyleSheet("""
            QLabel {
                color: white;
                background-color: rgba(255,255,255,0.2);
                border-radius: 8px;
                padding: 12px;
                margin: 5px;
            }
        """)

        monthly_summary_layout.addWidget(self.monthly_total_label)
        monthly_summary_layout.addWidget(self.monthly_paid_label)
        monthly_summary_layout.addWidget(self.monthly_remaining_label)
        monthly_layout.addLayout(monthly_summary_layout)

        # Monthly orders list
        monthly_orders_label = QLabel("قائمة طلبات الشهر:")
        monthly_orders_label.setFont(QFont("Traditional Arabic", 16, QFont.Bold))
        monthly_orders_label.setStyleSheet("color: white; margin-top: 15px;")
        monthly_layout.addWidget(monthly_orders_label)

        self.monthly_orders_list = QListWidget()
        self.monthly_orders_list.setFont(QFont("Traditional Arabic", 14))
        self.monthly_orders_list.setStyleSheet("""
            QListWidget {
                background-color: white;
                border-radius: 8px;
                border: 2px solid #ba4a00;
                padding: 5px;
            }
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #ecf0f1;
            }
            QListWidget::item:selected {
                background-color: #e67e22;
                color: white;
            }
        """)
        monthly_layout.addWidget(self.monthly_orders_list)

        layout.addWidget(monthly_section)

        # Load initial data for the lists
        self.load_daily_orders()
        self.load_monthly_orders()

    # Customer methods
    def search_customers(self):
        """Search customers by name or phone."""
        search_text = self.customer_search.text().strip()
        self.customer_list.clear()

        if search_text:
            customers = self.db.search_customers(search_text)
        else:
            customers = self.db.get_all_customers()

        for customer in customers:
            item_text = f"{customer['name']} - {customer['phone']}"
            self.customer_list.addItem(item_text)
            # Store customer data in item
            item = self.customer_list.item(self.customer_list.count() - 1)
            item.setData(Qt.UserRole, customer)

    def on_customer_selected(self, current, previous):
        """Handle customer selection from list."""
        # Suppress unused parameter warning
        _ = previous
        if current:
            customer = current.data(Qt.UserRole)
            if customer:
                self.load_customer_data(customer)

    def load_customer_data(self, customer):
        """Load customer data into form fields."""
        self.customer_name.setText(customer.get('name', ''))
        self.customer_phone.setText(customer.get('phone', ''))
        self.customer_chest.setText(str(customer.get('chest', '')))
        self.customer_shoulder.setText(str(customer.get('shoulder', '')))
        self.customer_hand.setText(str(customer.get('hand', '')))
        self.customer_waist.setText(str(customer.get('waist', '')))
        self.customer_hip.setText(str(customer.get('hip', '')))
        self.customer_inseam.setText(str(customer.get('inseam', '')))
        self.customer_neck.setText(str(customer.get('neck', '')))
        self.customer_height.setText(str(customer.get('height', '')))

    def add_customer(self):
        """Add a new customer."""
        self.clear_customer_form()

    def edit_customer(self):
        """Edit the selected customer."""
        current_item = self.customer_list.currentItem()
        if not current_item:
            self.show_message("يرجى اختيار عميل للتعديل")
            return

        customer = current_item.data(Qt.UserRole)
        if customer:
            self.load_customer_data(customer)

    def delete_customer(self):
        """Delete the selected customer."""
        current_item = self.customer_list.currentItem()
        if not current_item:
            self.show_message("يرجى اختيار عميل للحذف")
            return

        customer = current_item.data(Qt.UserRole)
        if customer:
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف العميل '{customer['name']}'؟\nسيتم حذف جميع الطلبات والديون المرتبطة بهذا العميل.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    self.db.delete_customer(customer['id'])
                    self.show_message("تم حذف العميل بنجاح")
                    self.refresh_customers()
                    self.clear_customer_form()
                except Exception as e:
                    self.show_message(f"خطأ في حذف العميل: {str(e)}")

    def save_customer(self):
        """Save customer information."""
        name = self.customer_name.text().strip()
        phone = self.customer_phone.text().strip()

        if not name or not phone:
            self.show_message("يرجى ملء الاسم ورقم الهاتف")
            return

        customer_data = {
            'name': name,
            'phone': phone,
            'chest': self.get_float_value(self.customer_chest.text()),
            'shoulder': self.get_float_value(self.customer_shoulder.text()),
            'hand': self.get_float_value(self.customer_hand.text()),
            'waist': self.get_float_value(self.customer_waist.text()),
            'hip': self.get_float_value(self.customer_hip.text()),
            'inseam': self.get_float_value(self.customer_inseam.text()),
            'neck': self.get_float_value(self.customer_neck.text()),
            'height': self.get_float_value(self.customer_height.text())
        }

        try:
            current_item = self.customer_list.currentItem()
            if current_item and current_item.data(Qt.UserRole):
                # Update existing customer
                customer = current_item.data(Qt.UserRole)
                customer_data['id'] = customer['id']
                self.db.update_customer(customer_data)
                self.show_message("تم تحديث بيانات العميل بنجاح")
            else:
                # Add new customer
                self.db.add_customer(customer_data)
                self.show_message("تم إضافة العميل بنجاح")

            self.refresh_customers()
            self.clear_customer_form()
        except Exception as e:
            self.show_message(f"خطأ في حفظ بيانات العميل: {str(e)}")

    def clear_customer_form(self):
        """Clear customer form fields."""
        self.customer_name.clear()
        self.customer_phone.clear()
        self.customer_chest.clear()
        self.customer_shoulder.clear()
        self.customer_hand.clear()
        self.customer_waist.clear()
        self.customer_hip.clear()
        self.customer_inseam.clear()
        self.customer_neck.clear()
        self.customer_height.clear()

    def refresh_customers(self):
        """Refresh customer list."""
        self.search_customers()

    def get_float_value(self, text):
        """Convert text to float, return None if empty or invalid."""
        try:
            return float(text) if text.strip() else None
        except ValueError:
            return None

    def show_message(self, message):
        """Show message to user."""
        QMessageBox.information(self, "معاريس الجهراء", message)

    # Order methods
    def search_customers_for_order(self):
        """Search customers for order."""
        search_text = self.order_customer_search.text().strip()
        self.order_customer_list.clear()

        if search_text:
            customers = self.db.search_customers(search_text)
        else:
            customers = self.db.get_all_customers()

        for customer in customers:
            item_text = f"{customer['name']} - {customer['phone']}"
            self.order_customer_list.addItem(item_text)
            # Store customer data in item
            item = self.order_customer_list.item(self.order_customer_list.count() - 1)
            item.setData(Qt.UserRole, customer)

    def on_order_customer_selected(self, current, previous):
        """Handle customer selection from list for orders."""
        # Suppress unused parameter warning
        _ = previous
        if current:
            customer = current.data(Qt.UserRole)
            if customer:
                self.order_customer_name.setText(f"{customer['name']} - {customer['phone']}")
                self.load_customer_orders(customer['id'])

    def on_order_selected(self, current, previous):
        """Handle order selection from list."""
        # Suppress unused parameter warning
        _ = previous
        if current:
            order = current.data(Qt.UserRole)
            if order:
                self.load_order_data(order)

    def load_order_data(self, order):
        """Load order data into form fields."""
        self.order_type.setCurrentText(order.get('order_type', 'صيفي'))
        self.order_total_price.setText(str(order.get('total_price', '')))
        self.order_payment.setText(str(order.get('payment', '')))
        self.current_order_id = order.get('id')
        self.calculate_remaining()

    def refresh_order_statistics(self):
        """Refresh the daily and monthly order statistics."""
        self.load_daily_orders()
        self.load_monthly_orders()

    def load_customer_orders(self, customer_id):
        """Load orders for a specific customer."""
        self.orders_list.clear()
        orders = self.db.get_customer_orders(customer_id)

        for order in orders:
            remaining = order['total_price'] - order['payment']
            item_text = f"#{order['id']} - {order['order_type']} - {order['total_price']} د.ك (متبقي: {remaining} د.ك)"
            self.orders_list.addItem(item_text)
            # Store order data in item
            item = self.orders_list.item(self.orders_list.count() - 1)
            item.setData(Qt.UserRole, order)

    def calculate_remaining(self):
        """Calculate remaining amount for order."""
        try:
            total = float(self.order_total_price.text() or 0)
            payment = float(self.order_payment.text() or 0)
            remaining = total - payment
            self.order_remaining.setText(f"{remaining:.3f}")

            # Update color based on remaining amount
            if remaining > 0:
                self.order_remaining.setStyleSheet("color: #e74c3c; font-weight: bold;")
            else:
                self.order_remaining.setStyleSheet("color: #27ae60; font-weight: bold;")
        except ValueError:
            self.order_remaining.setText("0")

    def add_order(self):
        """Add a new order."""
        self.clear_order_form()

    def edit_order(self):
        """Edit the selected order."""
        current_item = self.orders_list.currentItem()
        if not current_item:
            self.show_message("يرجى اختيار طلب للتعديل")
            return

        order = current_item.data(Qt.UserRole)
        if order:
            self.load_order_data(order)

    def delete_order(self):
        """Delete the selected order - FAST IMPLEMENTATION."""
        current_item = self.orders_list.currentItem()
        if not current_item:
            self.show_message("يرجى اختيار طلب للحذف")
            return

        order = current_item.data(Qt.UserRole)
        if order:
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف الطلب؟\n\nرقم الطلب: #{order['id']}\nنوع الطلب: {order['order_type']}\nالمبلغ: {order['total_price']} د.ك\n\nسيتم حذف الطلب والدين المرتبط به (إن وجد).",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                try:
                    # Fast delete - direct database operation
                    self.db.delete_order(order['id'])
                    self.show_message("تم حذف الطلب بنجاح")

                    # Instant UI update
                    current_customer = self.order_customer_list.currentItem()
                    if current_customer:
                        customer = current_customer.data(Qt.UserRole)
                        if customer:
                            self.load_customer_orders(customer['id'])

                    self.clear_order_form()
                    self.refresh_order_statistics()
                except Exception as e:
                    self.show_message(f"خطأ في حذف الطلب: {str(e)}")

    def save_order(self):
        """Save order information."""
        current_customer = self.order_customer_list.currentItem()
        if not current_customer:
            self.show_message("يرجى اختيار عميل")
            return

        customer = current_customer.data(Qt.UserRole)
        if not customer:
            self.show_message("يرجى اختيار عميل")
            return

        try:
            total_price = float(self.order_total_price.text() or 0)
            payment = float(self.order_payment.text() or 0)
        except ValueError:
            self.show_message("يرجى إدخال أرقام صحيحة للمبالغ")
            return

        if total_price <= 0:
            self.show_message("يرجى إدخال سعر إجمالي صحيح")
            return

        if payment > total_price:
            self.show_message("المبلغ المدفوع لا يمكن أن يكون أكبر من السعر الإجمالي")
            return

        order_data = {
            'customer_id': customer['id'],
            'order_type': self.order_type.currentText(),
            'total_price': total_price,
            'payment': payment
        }

        try:
            if self.current_order_id:
                # Update existing order
                order_data['id'] = self.current_order_id
                self.db.update_order(order_data)
                self.show_message("تم تحديث الطلب بنجاح")
            else:
                # Add new order
                self.db.add_order(order_data)
                remaining = total_price - payment
                message = "تم إضافة الطلب بنجاح"
                if remaining > 0:
                    message += f"\nتم إنشاء دين بقيمة {remaining:.3f} د.ك"
                self.show_message(message)

            self.load_customer_orders(customer['id'])
            self.clear_order_form()
            self.refresh_order_statistics()
        except Exception as e:
            self.show_message(f"خطأ في حفظ الطلب: {str(e)}")

    def clear_order_form(self):
        """Clear order form fields."""
        self.order_type.setCurrentIndex(0)
        self.order_total_price.clear()
        self.order_payment.clear()
        self.order_remaining.setText("0")
        self.current_order_id = None

    # Debt methods
    def search_customers_with_debt(self):
        """Search customers with debt."""
        pass

    def on_debt_customer_selected(self, current, previous):
        """Handle debt customer selection from list."""
        # Parameters are intentionally unused in base implementation
        _ = current, previous
        pass

    def calculate_debt_remaining(self):
        """Calculate remaining debt."""
        pass

    def add_debt(self):
        """Add a new debt record."""
        pass

    def edit_debt(self):
        """Edit the selected debt record."""
        pass

    def pay_debt(self):
        """Pay debt for the selected customer."""
        pass

    def save_debt(self):
        """Save debt information."""
        pass

    def load_daily_orders(self):
        """Load today's orders into the daily orders list."""
        # Sample data for demonstration
        sample_daily_orders = [
            "أحمد محمد - طلب صيفي - 150.00 د.ك",
            "فاطمة علي - طلب شتوي - 180.00 د.ك",
            "محمد خالد - طلب صيفي - 120.00 د.ك",
            "نورا سعد - طلب شتوي - 200.00 د.ك"
        ]

        self.daily_orders_list.clear()
        for order in sample_daily_orders:
            self.daily_orders_list.addItem(order)

    def load_monthly_orders(self):
        """Load this month's orders into the monthly orders list."""
        # Sample data for demonstration
        sample_monthly_orders = [
            "أحمد محمد - 3 طلبات - 450.00 د.ك",
            "فاطمة علي - 2 طلبات - 360.00 د.ك",
            "محمد خالد - 4 طلبات - 480.00 د.ك",
            "نورا سعد - 1 طلب - 200.00 د.ك",
            "سارة أحمد - 2 طلبات - 320.00 د.ك",
            "عبدالله محمد - 3 طلبات - 540.00 د.ك"
        ]

        self.monthly_orders_list.clear()
        for order in sample_monthly_orders:
            self.monthly_orders_list.addItem(order)

    def go_back(self):
        """Handle back button click to return to the previous screen."""
        # This will be implemented in the subclasses
        pass


