#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
معاريس الجهراء - واجهة الموظف
"""

from PySide6.QtWidgets import QMessageBox, QListWidgetItem
from PySide6.QtCore import Qt
from datetime import datetime

from ui.base_interface import BaseInterface
from ui.role_selection import RoleSelectionScreen

class EmployeeInterface(BaseInterface):
    def __init__(self):
        super().__init__(role="موظف")

        # Initialize UI with data
        self.load_customers()
        self.load_customers_with_debt()
        self.search_customers_for_order()  # Load customers in the orders tab
        self.refresh_order_statistics()    # Load initial statistics

        # Set up employee-specific UI elements and restrictions
        self.setup_employee_ui()

    def setup_employee_ui(self):
        """Setup employee-specific UI elements and restrictions."""
        # Employees can't delete customers
        self.delete_customer_btn.setEnabled(False)
        self.delete_customer_btn.setVisible(False)

    # Customer methods
    def load_customers(self):
        """Load all customers into the list."""
        self.customer_list.clear()

        # Get all customers from database
        self.cursor = self.db.conn.cursor()
        self.cursor.execute("SELECT id, name, phone FROM customers ORDER BY name")
        customers = self.cursor.fetchall()

        # Add customers to list
        for customer in customers:
            item = QListWidgetItem(f"{customer[1]} - {customer[2]}")
            item.setData(Qt.UserRole, customer[0])  # Store customer ID
            self.customer_list.addItem(item)

    def search_customers(self):
        """Search customers by name or phone."""
        search_term = self.customer_search.text().strip()

        if not search_term:
            self.load_customers()
            return

        self.customer_list.clear()

        # Search customers in database
        customers = self.db.search_customers(search_term)

        # Add matching customers to list
        for customer in customers:
            item = QListWidgetItem(f"{customer[1]} - {customer[2]}")
            item.setData(Qt.UserRole, customer[0])  # Store customer ID
            self.customer_list.addItem(item)

    def on_customer_selected(self, current, previous):
        """Handle customer selection from list."""
        if not current:
            return

        # Get customer ID
        customer_id = current.data(Qt.UserRole)

        # Get customer details from database
        customer = self.db.get_customer(customer_id)

        if customer:
            # Populate form fields
            self.customer_name.setText(customer[1])
            self.customer_phone.setText(customer[2])
            self.customer_chest.setText(str(customer[3]))
            self.customer_shoulder.setText(str(customer[4]))
            self.customer_hand.setText(str(customer[5]))
            self.customer_waist.setText(str(customer[6]))
            self.customer_hip.setText(str(customer[7]))
            self.customer_inseam.setText(str(customer[8]))
            self.customer_neck.setText(str(customer[9]))
            self.customer_height.setText(str(customer[10]))

            # Store current customer ID
            self.current_customer_id = customer_id

    def add_customer(self):
        """Add a new customer."""
        # Clear form fields
        self.customer_name.clear()
        self.customer_phone.clear()
        self.customer_chest.clear()
        self.customer_shoulder.clear()
        self.customer_hand.clear()
        self.customer_waist.clear()
        self.customer_hip.clear()
        self.customer_inseam.clear()
        self.customer_neck.clear()
        self.customer_height.clear()

        # Set current customer ID to None (new customer)
        self.current_customer_id = None

    def edit_customer(self):
        """Edit the selected customer."""
        # Nothing to do here, as the form is already populated
        pass

    def delete_customer(self):
        """Delete the selected customer."""
        # Employees can't delete customers
        QMessageBox.warning(self, "تم رفض الوصول", "غير مسموح للموظفين بحذف العملاء.")

    def save_customer(self):
        """Save customer information."""
        # Get form values
        name = self.customer_name.text().strip()
        phone = self.customer_phone.text().strip()

        # Validate required fields
        if not name:
            QMessageBox.warning(self, "تحذير", "اسم العميل مطلوب.")
            return

        # Get measurements
        try:
            chest = float(self.customer_chest.text()) if self.customer_chest.text() else 0
            shoulder = float(self.customer_shoulder.text()) if self.customer_shoulder.text() else 0
            hand = float(self.customer_hand.text()) if self.customer_hand.text() else 0
            waist = float(self.customer_waist.text()) if self.customer_waist.text() else 0
            hip = float(self.customer_hip.text()) if self.customer_hip.text() else 0
            inseam = float(self.customer_inseam.text()) if self.customer_inseam.text() else 0
            neck = float(self.customer_neck.text()) if self.customer_neck.text() else 0
            height = float(self.customer_height.text()) if self.customer_height.text() else 0
        except ValueError:
            QMessageBox.warning(self, "تحذير", "يجب أن تكون القياسات قيم رقمية.")
            return

        # Save to database
        if hasattr(self, 'current_customer_id') and self.current_customer_id is not None:
            # Update existing customer
            self.db.update_customer(
                self.current_customer_id, name, phone,
                chest, shoulder, hand, waist, hip, inseam, neck, height
            )
            message = "تم تحديث بيانات العميل بنجاح."
        else:
            # Add new customer
            self.current_customer_id = self.db.add_customer(
                name, phone, chest, shoulder, hand, waist, hip, inseam, neck, height
            )
            message = "تمت إضافة العميل بنجاح."

        # Reload customers
        self.load_customers()

        QMessageBox.information(self, "نجاح", message)

    # Order methods
    def search_customers_for_order(self):
        """Search customers for order."""
        search_term = self.order_customer_search.text().strip()

        self.order_customer_list.clear()

        if not search_term:
            # Load all customers
            self.cursor = self.db.conn.cursor()
            self.cursor.execute("SELECT id, name, phone FROM customers ORDER BY name")
            customers = self.cursor.fetchall()
        else:
            # Search customers in database
            customers = self.db.search_customers(search_term)

        # Add customers to list
        for customer in customers:
            item = QListWidgetItem(f"{customer[1]} - {customer[2]}")
            item.setData(Qt.UserRole, customer[0])  # Store customer ID
            self.order_customer_list.addItem(item)

    def on_order_customer_selected(self, current, previous):
        """Handle customer selection from list for orders."""
        if not current:
            return

        # Get customer ID
        customer_id = current.data(Qt.UserRole)

        # Get customer details from database
        customer = self.db.get_customer(customer_id)

        if customer:
            # Set customer name
            self.order_customer_name.setText(f"{customer[1]} - {customer[2]}")

            # Store current customer ID
            self.current_order_customer_id = customer_id

            # Clear form fields for new order
            self.add_order()

            # Load customer orders
            self.load_customer_orders(customer_id)

    def on_order_selected(self, current, previous):
        """Handle order selection from list."""
        if not current:
            return

        # Get order ID
        order_id = current.data(Qt.UserRole)

        # Get order details from database
        order = self.db.get_order(order_id)

        if order:
            # Populate form fields
            self.current_order_id = order[0]
            self.order_type.setCurrentText(order[2])
            self.order_total_price.setText(str(order[3]))
            self.order_payment.setText(str(order[4]))
            self.order_remaining.setText(str(order[5]))

    def refresh_order_statistics(self):
        """Refresh the daily and monthly order statistics."""
        # Get daily statistics
        daily_stats = self.db.get_daily_orders_total()
        self.daily_total_label.setText(f"{daily_stats['total']:.2f}")
        self.daily_paid_label.setText(f"{daily_stats['paid']:.2f}")
        self.daily_remaining_label.setText(f"{daily_stats['remaining']:.2f}")

        # Get monthly statistics
        monthly_stats = self.db.get_monthly_orders_total()
        self.monthly_total_label.setText(f"{monthly_stats['total']:.2f}")
        self.monthly_paid_label.setText(f"{monthly_stats['paid']:.2f}")
        self.monthly_remaining_label.setText(f"{monthly_stats['remaining']:.2f}")

    def load_customer_orders(self, customer_id):
        """Load orders for a specific customer."""
        self.orders_list.clear()

        # Get orders from database
        self.cursor = self.db.conn.cursor()
        self.cursor.execute('''
        SELECT o.id, o.order_type, o.total_price, o.payment, o.remaining, o.created_at
        FROM orders o
        WHERE o.customer_id = ?
        ORDER BY o.created_at DESC
        ''', (customer_id,))

        orders = self.cursor.fetchall()

        # Add orders to list
        for order in orders:
            created_at = datetime.strptime(order[5], '%Y-%m-%d %H:%M:%S').strftime('%Y-%m-%d')
            item = QListWidgetItem(f"{order[1]} - {order[2]:.2f} - {created_at}")
            item.setData(Qt.UserRole, order[0])  # Store order ID
            self.orders_list.addItem(item)

    def calculate_remaining(self):
        """Calculate remaining amount for order."""
        try:
            total_price = float(self.order_total_price.text()) if self.order_total_price.text() else 0
            payment = float(self.order_payment.text()) if self.order_payment.text() else 0
            remaining = total_price - payment
            self.order_remaining.setText(f"{remaining:.2f}")
        except ValueError:
            self.order_remaining.setText("قيمة غير صحيحة")

    def add_order(self):
        """Add a new order."""
        # Clear form fields
        self.order_type.setCurrentIndex(0)
        self.order_total_price.clear()
        self.order_payment.clear()
        self.order_remaining.setText("0")

        # Set current order ID to None (new order)
        self.current_order_id = None

        # Clear selection in orders list
        self.orders_list.clearSelection()

    def edit_order(self):
        """Edit the selected order."""
        if not self.orders_list.currentItem():
            QMessageBox.warning(self, "تحذير", "الرجاء اختيار طلب للتعديل.")
            return

    def delete_order(self):
        """Delete the selected order."""
        if not hasattr(self, 'current_order_id') or self.current_order_id is None:
            QMessageBox.warning(self, "تحذير", "لم يتم اختيار طلب.")
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا الطلب؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Delete order from database
            self.db.delete_order(self.current_order_id)

            # Reload customer orders
            self.load_customer_orders(self.current_order_customer_id)

            # Clear form fields
            self.add_order()

            # Refresh statistics
            self.refresh_order_statistics()

            QMessageBox.information(self, "نجاح", "تم حذف الطلب بنجاح.")

    def save_order(self):
        """Save order information."""
        if not hasattr(self, 'current_order_customer_id') or self.current_order_customer_id is None:
            QMessageBox.warning(self, "تحذير", "لم يتم اختيار عميل.")
            return

        # Get form values
        order_type = self.order_type.currentText()

        # Validate numeric fields
        try:
            total_price = float(self.order_total_price.text()) if self.order_total_price.text() else 0
            payment = float(self.order_payment.text()) if self.order_payment.text() else 0
        except ValueError:
            QMessageBox.warning(self, "تحذير", "يجب أن تكون قيم السعر والدفع أرقامًا.")
            return

        # Save to database
        if hasattr(self, 'current_order_id') and self.current_order_id is not None:
            # Update existing order
            self.db.update_order(
                self.current_order_id, order_type, total_price, payment
            )
            message = "تم تحديث الطلب بنجاح."
        else:
            # Add new order
            self.current_order_id = self.db.add_order(
                self.current_order_customer_id, order_type, total_price, payment
            )
            message = "تمت إضافة الطلب بنجاح."

        # If there's remaining balance, add to debt
        remaining = total_price - payment
        if remaining > 0:
            self.db.add_debt(self.current_order_customer_id, remaining, 0)
            message += " تم إنشاء سجل دين للرصيد المتبقي."

            # Reload customers with debt
            self.load_customers_with_debt()

        # Reload customer orders
        self.load_customer_orders(self.current_order_customer_id)

        # Refresh statistics
        self.refresh_order_statistics()

        QMessageBox.information(self, "نجاح", message)

    # Debt methods
    def load_customers_with_debt(self):
        """Load customers with debt into the list."""
        self.debt_customer_list.clear()

        # Get customers with debt from database
        customers = self.db.get_customers_with_debt()

        # Add customers to list
        for customer in customers:
            item = QListWidgetItem(f"{customer[1]} - {customer[2]}")
            item.setData(Qt.UserRole, customer[0])  # Store customer ID
            self.debt_customer_list.addItem(item)

    def search_customers_with_debt(self):
        """Search customers with debt."""
        search_term = self.debt_customer_search.text().strip()

        if not search_term:
            self.load_customers_with_debt()
            return

        self.debt_customer_list.clear()

        # Get all customers with debt
        customers = self.db.get_customers_with_debt()

        # Filter by search term
        for customer in customers:
            if search_term.lower() in customer[1].lower() or search_term in customer[2]:
                item = QListWidgetItem(f"{customer[1]} - {customer[2]}")
                item.setData(Qt.UserRole, customer[0])  # Store customer ID
                self.debt_customer_list.addItem(item)

    def on_debt_customer_selected(self, current, previous):
        """Handle debt customer selection from list."""
        if not current:
            return

        # Get customer ID
        customer_id = current.data(Qt.UserRole)

        # Get customer details from database
        customer = self.db.get_customer(customer_id)

        if customer:
            # Set customer name
            self.debt_customer_name.setText(f"{customer[1]} - {customer[2]}")

            # Get debt records for customer
            debts = self.db.get_customer_debts(customer_id)

            if debts:
                # Use the most recent debt record
                debt = debts[-1]
                self.debt_total_due.setText(str(debt[2]))
                self.debt_paid.setText(str(debt[3]))
                self.debt_remaining.setText(str(debt[4]))

                # Store current debt ID and customer ID
                self.current_debt_id = debt[0]
                self.current_debt_customer_id = customer_id
            else:
                # No debt records found
                self.debt_total_due.clear()
                self.debt_paid.clear()
                self.debt_remaining.setText("0")

                # Store customer ID but no debt ID
                self.current_debt_id = None
                self.current_debt_customer_id = customer_id

    def calculate_debt_remaining(self):
        """Calculate remaining debt."""
        try:
            total_due = float(self.debt_total_due.text()) if self.debt_total_due.text() else 0
            paid = float(self.debt_paid.text()) if self.debt_paid.text() else 0
            remaining = total_due - paid
            self.debt_remaining.setText(f"{remaining:.2f}")
        except ValueError:
            self.debt_remaining.setText("قيمة غير صحيحة")

    def add_debt(self):
        """Add a new debt record."""
        # Clear form fields
        self.debt_total_due.clear()
        self.debt_paid.clear()
        self.debt_remaining.setText("0")

        # Set current debt ID to None (new debt)
        self.current_debt_id = None

    def edit_debt(self):
        """Edit the selected debt record."""
        # Nothing to do here, as the form is already populated
        pass

    def pay_debt(self):
        """Pay debt for the selected customer."""
        if not hasattr(self, 'current_debt_customer_id') or self.current_debt_customer_id is None:
            QMessageBox.warning(self, "تحذير", "لم يتم اختيار عميل.")
            return

        # Get current debt details
        try:
            total_due = float(self.debt_total_due.text()) if self.debt_total_due.text() else 0
            current_paid = float(self.debt_paid.text()) if self.debt_paid.text() else 0
        except ValueError:
            QMessageBox.warning(self, "تحذير", "قيم الدين غير صحيحة.")
            return

        # Ask for payment amount
        from PySide6.QtWidgets import QInputDialog
        payment, ok = QInputDialog.getDouble(
            self, "الدفع", "أدخل مبلغ الدفع:", 0, 0, total_due - current_paid, 2
        )

        if ok and payment > 0:
            # Update paid amount
            new_paid = current_paid + payment
            self.debt_paid.setText(str(new_paid))

            # Calculate remaining
            self.calculate_debt_remaining()

            # Save debt
            self.save_debt()

    def save_debt(self):
        """Save debt information."""
        if not hasattr(self, 'current_debt_customer_id') or self.current_debt_customer_id is None:
            QMessageBox.warning(self, "تحذير", "لم يتم اختيار عميل.")
            return

        # Validate numeric fields
        try:
            total_due = float(self.debt_total_due.text()) if self.debt_total_due.text() else 0
            paid = float(self.debt_paid.text()) if self.debt_paid.text() else 0
        except ValueError:
            QMessageBox.warning(self, "تحذير", "يجب أن تكون قيم الدين أرقامًا.")
            return

        # Save to database
        if hasattr(self, 'current_debt_id') and self.current_debt_id is not None:
            # Update existing debt
            self.db.update_debt(
                self.current_debt_id, total_due, paid
            )
            message = "تم تحديث الدين بنجاح."
        else:
            # Add new debt
            self.current_debt_id = self.db.add_debt(
                self.current_debt_customer_id, total_due, paid
            )
            message = "تمت إضافة الدين بنجاح."

        # Reload customers with debt
        self.load_customers_with_debt()

        QMessageBox.information(self, "نجاح", message)

    def go_back(self):
        """Handle back button click to return to the role selection screen."""
        # Hide the current interface
        self.hide()

        # Import here to avoid circular imports
        from main import MainApplication

        # Create a new instance of MainApplication and show role selection
        self.main_app = MainApplication(skip_splash=True)

        # Close this window completely
        self.close()