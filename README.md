# Ma'aris Al Jahra - Customer Management System

A desktop application for managing customers, orders, and debts for a tailoring business.

## Features

- **Splash Screen**: Attractive startup screen with animations
- **Role Selection**: Choose between Manager and Employee roles
- **Customer Management**: Add, edit, and view customer information including measurements
- **Order Management**: Create and manage customer orders
- **Debt Tracking**: Track and manage customer debts

## Requirements

- Python 3.6+
- PySide6
- SQLite3 (included with Python)

## Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/maaris-aljahra.git
   cd maaris-aljahra
   ```

2. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

3. Run the application:
   ```
   python main.py
   ```

## Usage

### Splash Screen
The application starts with a splash screen displaying the "Ma'aris Al Jahra" logo with animation effects. After 3 seconds, it automatically transitions to the role selection screen.

### Role Selection
Choose between:
- **Manager**: Full access to all features
- **Employee**: Limited access (cannot delete customers)

### Customer Management
- Search for customers by name or phone number
- Add new customers with their measurements
- Edit existing customer information
- View customer details

### Order Management
- Create new orders for customers
- Select order type (Summer/Winter)
- Enter total price and payment
- Automatically calculate remaining balance
- Automatically create debt records for unpaid balances

### Debt Management
- View customers with outstanding debts
- Record payments against debts
- Track remaining balances

## Project Structure

```
maaris-aljahra/
├── main.py                  # Main application entry point
├── database.py              # Database operations
├── requirements.txt         # Required packages
├── README.md                # This file
├── ui/                      # UI components
│   ├── __init__.py
│   ├── splash_screen.py     # Splash screen implementation
│   ├── role_selection.py    # Role selection screen
│   ├── base_interface.py    # Base interface for manager and employee
│   ├── manager_interface.py # Manager-specific interface
│   └── employee_interface.py# Employee-specific interface
└── resources/               # Application resources
    └── style.qss            # Application stylesheet
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Author

Your Name

## Acknowledgments

- PySide6 for the Qt framework
- SQLite for the database engine
