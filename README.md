# معاريس الجهراء - <PERSON><PERSON><PERSON><PERSON>

A modern C# Windows Forms application for customer data management with beautiful UI and smooth animations.

## Features

- **Modern UI Design**: Gradient backgrounds, rounded corners, and smooth animations
- **Arabic Language Support**: Full RTL support with Arabic interface
- **Worker Management**: Add, edit, delete, and manage workers
- **Order Management**: Create orders with automatic debt tracking
- **Debt Management**: Track and manage customer debts
- **Automatic Data Sync**: Real-time updates across all sections
- **SQLite Database**: Local database with Entity Framework Core
- **Beautiful Animations**: Smooth transitions, hover effects, and loading animations

## Technology Stack

- **Framework**: .NET 6 Windows Forms
- **Database**: SQLite with Entity Framework Core
- **UI Libraries**: 
  - Bunifu.UI.WinForms
  - Guna.UI2.WinForms
  - FontAwesome.Sharp
- **Language**: C# with async/await patterns

## Prerequisites

- .NET 6.0 SDK or later
- Visual Studio 2022 or Visual Studio Code
- Windows 10/11

## Installation & Setup

1. **Clone or download the project**
2. **Restore NuGet packages**:
   ```bash
   dotnet restore
   ```

3. **Build the project**:
   ```bash
   dotnet build
   ```

4. **Run the application**:
   ```bash
   dotnet run
   ```

## Project Structure

```
MaarisAlJahra/
├── Forms/
│   ├── SplashForm.cs          # Splash screen with loading animation
│   ├── LoginForm.cs           # User type selection
│   ├── ManagerForm.cs         # Main dashboard
│   ├── WorkersForm.cs         # Worker management
│   ├── OrdersForm.cs          # Order management
│   └── DebtsForm.cs           # Debt management
├── Models/
│   ├── Worker.cs              # Worker entity
│   ├── Order.cs               # Order entity
│   └── Debt.cs                # Debt entity
├── Data/
│   └── DatabaseContext.cs    # EF Core context
├── Helpers/
│   ├── AnimationHelper.cs     # Animation utilities
│   ├── StyleHelper.cs         # UI styling utilities
│   └── CustomMessageBox.cs   # Custom message boxes
└── Program.cs                 # Application entry point
```

## Key Features

### 1. Splash Screen
- 3-second auto-close with fade effects
- Loading progress animation
- Beautiful gradient background

### 2. Login System
- Manager and Employee access levels
- Animated buttons with hover effects
- Smooth transitions between forms

### 3. Manager Dashboard
- Three main sections: Workers, Orders, Debts
- Hover zoom effects on panels
- FontAwesome icons integration

### 4. Worker Management
- Add, edit, delete workers
- Soft delete functionality
- Real-time data validation
- Salary tracking

### 5. Order Management
- Customer order creation
- Automatic debt calculation
- Payment tracking
- Order status management

### 6. Debt Management
- Automatic debt creation from orders
- Payment tracking
- Debt settlement functionality
- Total debt calculation

## Database Schema

The application uses SQLite with the following tables:

- **Workers**: Employee information and salary data
- **Orders**: Customer orders with payment tracking
- **Debts**: Outstanding customer debts linked to orders

## UI Components

### Animations
- Fade in/out effects
- Scale animations on button clicks
- Shake effects for validation errors
- Smooth form transitions

### Styling
- Gradient backgrounds (#2A3F54 to #4A90E2)
- Rounded corners on all panels
- Material Design text boxes
- Custom styled DataGridViews
- Shadow effects on buttons

## Error Handling

- Comprehensive try-catch blocks
- User-friendly error messages in Arabic
- Input validation with visual feedback
- Database connection error handling

## Performance Features

- Async/await for all database operations
- Efficient LINQ queries
- Lazy loading of data
- Optimized UI rendering

## Customization

The application is designed to be easily customizable:

- Colors can be modified in `StyleHelper.cs`
- Animations can be adjusted in `AnimationHelper.cs`
- Database schema can be extended in the Models folder
- New forms can be added following the existing pattern

## Troubleshooting

1. **Package Restore Issues**: Run `dotnet restore` in the project directory
2. **Database Issues**: Delete `MaarisAlJahra.db` to recreate the database
3. **UI Issues**: Ensure all NuGet packages are properly installed
4. **Animation Issues**: Check that the application is running on Windows with .NET 6+

## License

This project is for educational and commercial use.

## Support

For support and questions, please refer to the documentation or contact the development team.
