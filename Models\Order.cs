using System.ComponentModel.DataAnnotations;

namespace MaarisAlJahra.Models
{
    public class Order
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string CustomerName { get; set; } = string.Empty;
        
        [StringLength(20)]
        public string CustomerPhone { get; set; } = string.Empty;
        
        [Required]
        [StringLength(200)]
        public string OrderType { get; set; } = string.Empty;
        
        public decimal TotalPrice { get; set; }
        
        public decimal Payment { get; set; }
        
        public decimal Remaining => TotalPrice - Payment;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public bool IsCompleted { get; set; } = false;
    }
}
