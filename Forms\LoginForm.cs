using System;
using System.Drawing;
using System.Windows.Forms;
using MaarisAlJahra.Helpers;

namespace MaarisAlJahra.Forms
{
    public partial class LoginForm : Form
    {
        private Button managerButton;
        private Button employeeButton;
        private Label titleLabel;

        public LoginForm()
        {
            InitializeComponent();
            SetupForm();
            SetupControls();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 700);
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.WindowState = FormWindowState.Normal;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.Name = "LoginForm";
            this.Text = "تسجيل الدخول";

            this.ResumeLayout(false);
        }

        private void SetupForm()
        {
            this.SetStyle(ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.UserPaint |
                         ControlStyles.DoubleBuffer, true);

            StyleHelper.ApplyGradientBackground(this);
        }

        private void SetupControls()
        {
            // Title Label
            titleLabel = new Label
            {
                Text = "اختر نوع المستخدم",
                Font = new Font("Segoe UI", 32, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent,
                AutoSize = true,
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Manager Button
            managerButton = CreateAnimatedButton("مدير النظام", StyleHelper.AccentColor);
            managerButton.Click += (s, e) =>
            {
                try
                {
                    var managerForm = new ManagerForm();
                    managerForm.Show();
                    this.Hide();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح النموذج: {ex.Message}", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            // Employee Button
            employeeButton = CreateAnimatedButton("موظف", StyleHelper.SuccessColor);
            employeeButton.Click += (s, e) =>
            {
                MessageBox.Show("وظيفة الموظف قيد التطوير", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
            };

            // Position controls
            PositionControls();

            this.Controls.Add(titleLabel);
            this.Controls.Add(managerButton);
            this.Controls.Add(employeeButton);

            // Handle resize
            this.Resize += (s, e) => PositionControls();
        }

        private Button CreateAnimatedButton(string text, Color color)
        {
            var button = new Button
            {
                Text = text,
                Size = new Size(400, 150),
                BackColor = color,
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                UseVisualStyleBackColor = false
            };

            button.FlatAppearance.BorderSize = 0;
            StyleHelper.ApplyRoundedCorners(button, 20);

            // Add shadow effect
            var shadowPanel = new Panel
            {
                Size = new Size(button.Width + 10, button.Height + 10),
                BackColor = Color.FromArgb(50, 0, 0, 0)
            };
            StyleHelper.ApplyRoundedCorners(shadowPanel, 25);

            // Hover effects
            button.MouseEnter += (s, e) =>
            {
                button.BackColor = ControlPaint.Light(color, 0.3f);
            };

            button.MouseLeave += (s, e) =>
            {
                button.BackColor = color;
            };

            return button;
        }

        private void PositionControls()
        {
            // Center title
            titleLabel.Location = new Point(
                (this.Width - titleLabel.Width) / 2,
                this.Height / 3
            );

            // Position buttons horizontally with spacing
            var totalButtonWidth = managerButton.Width + employeeButton.Width + 50;
            var startX = (this.Width - totalButtonWidth) / 2;
            var buttonY = titleLabel.Bottom + 80;

            managerButton.Location = new Point(startX, buttonY);
            employeeButton.Location = new Point(startX + managerButton.Width + 50, buttonY);
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            // Removed fade animation for better performance
        }
    }
}
